<?php
/**
 * Plugin Name: Static Page Redirect for Guests
 * Description: Redirects all guest users to a static HTML page with its assets.
 * Version: 2.5.6
 */

if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly.
}

class Static_Template_Plugin {
	public function __construct() {
		// Hook into the template_redirect action
		add_action('template_redirect', [$this, 'serve_static_template']);
	}

	public function serve_static_template() {
		if (!is_user_logged_in()) {
			// Get the current request URI and remove leading/trailing slashes
			$path = trim($_SERVER['REQUEST_URI'], '/');

			// Remove any query strings
			$path = strtok($path, '?');

			// Sanitize the path
			$path = preg_replace('/[^a-zA-Z0-9-_]/', '', $path);

			// First try to find a PHP file matching the path
			$template_file = plugin_dir_path(__FILE__) . 'static-page/' . $path . '.php';

			// If the specific file doesn't exist, fall back to static.php
			if (!file_exists($template_file)) {
				$template_file = plugin_dir_path(__FILE__) . 'static-page/static.php';
			}

			// Double check the file exists and is within our plugin directory
			$real_template = realpath($template_file);
			$plugin_dir = realpath(plugin_dir_path(__FILE__) . 'static-page');

			if ($real_template && strpos($real_template, $plugin_dir) === 0 && file_exists($real_template)) {
				include($real_template);
				exit; // Prevent further WordPress processing
			}
		}
	}
}

new Static_Template_Plugin();