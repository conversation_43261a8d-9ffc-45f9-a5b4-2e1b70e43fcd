@font-face {
    font-family: 'Nexa-font';
    src: url('../fonts/NexaBlack.otf') format('opentype');
    font-weight: 400;
    font-style: normal;
}

@font-face {  font-family: 'Nexa-font';
    src: url('../fonts/Nexa-Bold.otf') format('opentype');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Nexa-font';
    src: url('../fonts/Nexa-XBold-Italic.otf') format('opentype');
    font-weight: 700;
    font-style: italic;
}

@font-face {  font-family: 'Nexa-font';
    src: url('../fonts/NexaHeavy.otf') format('opentype');
    font-weight: 800;
    font-style: normal;
}

@font-face {
    font-family: 'Nexa-font';
    src: url('../fonts/NexaRegular.otf') format('opentype');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Nexa-font';
    src: url('../fonts/Nexa-Light.otf') format('opentype');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'picadilly';
    src: url('../fonts/picadilly-bold.otf') format('opentype');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'poppins-light';
    src: url('../fonts/Poppins-Light.ttf') format('opentype');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'poppins';
    src: url('../fonts/Poppins-Regular.ttf') format('opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'poppins-medium';
    src: url('../fonts/Poppins-Medium.ttf') format('opentype');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'poppins-semibold';
    src: url('../fonts/Poppins-SemiBold.ttf') format('opentype');
    font-weight: 700;
    font-style: normal;
}