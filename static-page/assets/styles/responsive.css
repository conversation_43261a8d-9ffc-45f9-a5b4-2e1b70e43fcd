/* XL Device :1200px. */

@media (min-width: 1200px) and (max-width: 1449px) {
    .home_intro_section{
        width: 60%;
        margin: 50px auto;
    }

    .home_intro_section h2 {
        font-size: 40px;
    }

    .single-exclusive-thumb {
        height: 470px;
    }

    .hero__area {
        min-height: 690px;
        padding-top: 50px;
    }

    .hero__content h1 {
        font-size: 42px;
        margin-bottom: 0;
    }
}


/* LG Device :992px. */

@media (min-width: 992px) and (max-width: 1200px) {
    .home_intro_section{
        width: 80%;
        margin: 60px auto;
    }

   .home_intro_section h2 {
        font-size: 30px;
    }

    .single-exclusive-thumb {
        height: 470px;
    }

    .hero__area {
        min-height: 690px;
        padding-bottom: 150px;
        padding-top: 50px;
    }

    .hero__content h1 {
        font-size: 36px;
        margin-bottom: 15px;
    }


    .footer__identity img {
        height: 70px;
    }

    .footer__content a {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .footer__privacy a {
        font-size: 14px;
    }

    .commitment__content h4 {
        font-size: 28px;
        max-width: 445px;
        padding: 12px;
    }

    .exclusive__content h4 {
        font-size: 28px;
    }

    .commitment__logo a img {
        height: 240px;
    }

    .hero__content h5 {
        font-size: 16px;
    }

    .accordion-item-jc {
        height: 540px;
        padding-top: 150px;
        padding-left: 75px;
        padding-right: 75px;
    }
}


/* MD Device :768px. */

@media (min-width: 768px) and (max-width: 991px) {
    .home_intro_section{
        width: 100%;
        margin: 40px auto;
    }

    .home_intro_section h2 {
        font-size: 25px;
    }

    .image__grid {
        grid-template-columns: repeat(4, 1fr);
        grid-template-rows: repeat(2, auto);
    }

    .crafted__design__text{
        margin-left: 0;
        margin-right: 0;
        max-width: 100%;
    }

    .single-exclusive-thumb {
        height: 400px;
    }

    .hero__area {
        min-height: 690px;
        padding-bottom: 120px;
        padding-top: 50px;
    }

    .hero__content h1 {
        font-size: 36px;
        margin-bottom: 15px;
    }


    .footer__identity img {
        height: 70px;
    }

    .footer__content a {
        font-size: 12px;
        margin-bottom: 5px;
    }

    .footer__privacy a {
        font-size: 12px;
    }

    .commitment__content h4 {
        font-size: 28px;
        max-width: 100%;
        padding: 12px;
        margin: 0 auto;
        text-align: center;
    }

    .exclusive__content h4 {
        font-size: 28px;
        margin: 0;
    }

    .commitment__logo a img {
        height: 240px;
    }

    .hero__content h5 {
        font-size: 16px;
    }

    .accordion-item-jc {
        padding-top: 20px;
        padding-left: 30px;
        padding-right: 30px;
    }

    .hero__content p {
        font-size: 16px;
        margin: auto;
    }

    .accordion-header-jc {
        padding: 8px;
        font-size: 31px;
        padding-bottom: 0;
    }

    .accordion-content-jc {
        padding: 15px;
        font-size: 14px;
    }

    .commitment__content {
        min-height: auto;
        padding-bottom: 25px;
        margin-top: -26px;
    }

    .commitment__logo {
        justify-content: center;
        background: transparent;
    }

    .commitment__area {
        padding-top: 40px;
        margin-top: 40px;
        height: auto;
    }

    .commitment__logo:before {
        display: none;
    }

    .single__studio__contene h3 {
        font-size: 28px;
    }

    .single__studio__contene p {
        font-size: 16px;
    }


    .exclusive__inner__blk .owl-nav button {
        left: 12%;
    }

    .exclusive__inner__blk .owl-nav button.owl-next {
        right: 5%;
    }

    .header__area.sticky .header__logo a img {
        height: 15px;
    }

    .header__btn a {
        font-size: 14px;
    }

    .header__inner__blk {
        gap: 15px;
        height: 15px;
    }

    .studio__area .container {
        max-width: 100%;
    }

    .commitment__area {
        height: auto;
    }

    .header__btn a {
        font-size: 16px;
        padding: 19px 45px;
        line-height: 11px;
        padding-top: 24px;
    }

    .contact__btn__nav {
        padding: 0 !important;
        margin: 0 20px;
    }

    .contact__btn a {
        font-size: 16px;
        padding: 19px 45px;
        line-height: 11px;
        padding-top: 24px;
    }

    .hero_logo img {
        height: 45px;
    }

    .hero__inner__blk {
        padding: 20px;
        gap: 20px;
        padding-top: 90px;
    }

    .hero__inner__blk.sticky {
        padding-top: 20px;
    }

    .hero_logo {
        top: 20px;
    }
}


/* SM Small Device :320px. */
@media only screen and (max-width: 375px) {
#single-template .accordion-header-jc {
    font-size: 2rem !important;
    letter-spacing: 30px;
    left: 0;
    right: 0;
    margin: auto;
    justify-content: center;
}

}

@media only screen and (min-width: 320px) and (max-width: 767px) {
    #home .accordion-header-jc-hero {
        font-size: 35px;
        height: 30vh;
        letter-spacing: 1.5rem;
    }
    .image__grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(4, auto);
    }

    .crafted__design__text{
        margin-left: 0;
        margin-right: 0;
        max-width: 100%;
    }

    .banner__content span {
        font-size: 18px;
        letter-spacing: 6.3px;
    }

    .single-exclusive-thumb {
        height: 250px;
    }

    .studio__area .container {
        max-width: 100%;
    }

    .footer__copyright p {
        font-size: 12px;
    }

    .footer__mail a {
        font-size: 16px;
    }

    .hero_logo img {
        height: 25px;
    }

    .commitment__area {
        height: auto;
    }
    .hero__area {
        min-height: 50vh;
        padding-bottom: 130px;
        padding-top: 50px;
    }

    .hero__content h1 {
        font-size: 26px;
        margin-bottom: 15px;
    }


    .footer__identity img {
        height: 50px;
    }

    .footer__content a {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .footer__privacy a {
        font-size: 14px;
    }

    .commitment__content h4 {
        font-size: 20px;
        max-width: 100%;
        padding: 12px;
        margin: 0 auto;
        text-align: center;
    }

    .exclusive__content h4 {
        font-size: 28px;
        margin: 0;
    }

    .commitment__logo a img {
        height: 200px;
    }

    .hero__content h5 {
        font-size: 16px;
    }

    .accordion-item-jc {
        padding: 10px;
    }

    .hero__content p {
        font-size: 16px;
    }

    .accordion-header-jc {
        padding: 0;
        font-size: 20px;
        padding-bottom: 0;
    }

    .accordion-content-jc {
        padding: 0 !important;
        font-size: 14px;
        padding-top: 10px !important;
    }

    .commitment__content {
        min-height: auto;
        padding-bottom: 25px;
        margin-top: -26px;
    }

    .commitment__logo {
        justify-content: center;
        background: transparent;
    }

    .commitment__area {
        padding-top: 40px;
        margin-top: 40px;
    }

    .commitment__logo:before {
        display: none;
    }

    .single__studio__contene h3 {
        font-size: 18px;
    }

    .single__studio__contene p {
        font-size: 14px;
        display: none;
    }

    .exclusive__inner__blk .owl-nav button {
        left: 5%;
    }

    .exclusive__inner__blk .owl-nav button.owl-next {
        right: 5%;
    }

    .header__logo a img {
        height: auto;
        padding-left: 10px;
        margin-top: 70px;
    }

    .header__area.sticky .header__logo a img {
        height: 80px;
        padding-left: 40px;
        margin-top: 80px;
    }

    .header__btn a {
        font-size: 13px;
    }

    .header__inner__blk {
        gap: 15px;
        height: 15px;
    }

    .exclusive__content {
        margin-top: -11px;
    }

    .exclusive__content {
        margin-top: -11px;
    }

    .studio__right__blk {
        position: inherit;
        height: 370px;
        width: 100%;
    }

    .studio__left__blk {
        max-width: 100%;
        gap: 20px;
        flex-direction: row;
        justify-content: center;
    }

    .contact__area {
        padding-bottom: 30px;
    }

    .contact__btn a {
        font-size: 15px;
    }

    .studio__area {
        margin: 50px 0;
        height: auto;
    }

    .exclusive__inner__blk .owl-nav button {
        height: 40px;
        width: 40px;
        font-size: 24px !important;
    }

    .header__btn a {
        font-size: 14px;
        padding: 13px 18px;
        line-height: 12px;
        padding-top: 18px;
        width: 130px;
        text-align: center;
    }
    .contact__btn__nav {
        padding: 0 !important;
        margin: 0 20px;
    }

    .contact__btn a {
        font-size: 16px;
        padding: 19px 45px;
        line-height: 11px;
        padding-top: 24px;
    }

    .accordion-jc {
        height: 500px;
    }

    .footer__area {
        padding: 30px 0;
    }

    .hero_logo img {
        height: 25px;
    }

    .hero__inner__blk {
        padding: 20px;
        gap: 20px;
        padding-top: 58px;
    }

    .hero__inner__blk.sticky {
        padding-top: 20px;
    }

    .hero_logo {
        top: 20px;
    }

    .commitment__content img {
        padding-bottom: 2em;
        width: 100%;
    }
}

@media (max-width: 383px) {
    .image__grid {
        grid-template-columns: repeat(1, 1fr);
        grid-template-rows: repeat(8, auto);
    }

    .crafted__design__text{
        margin-left: 0;
        margin-right: 0;
        max-width: 100%;
    }

    .hero_logo img {
        height: 20px;
    }

    #expertise .accordion-header-jc {
        font-size: 20px !important;
    }

    .hero__content h4 {
        font-size: 20px;
        font-weight: 900;
    }

    .hero__content h5 {
        font-size: 18px !important;
    }

    #home .accordion_inner_jc {
        top: 60% !important;
    }

    .header__burger {
        padding-right: 1em;
    }

    .exclusive__content h4 {
        font-size: 20px;
        margin: 0;
        line-height: 25px;
    }

    #single-template .exclusive__content__blk p {
        font-size: 17px;
        padding-top: 0;
        padding-left: 0;
    }

    #single-template .bg_image .exclusive__content {
        display: none;
    }

    #single-template .bg_image {
        height: 1100px;
    }

    #single-template .white_bg {
        background: #ffffff;
        padding-top: 5em;
    }
    #single-template .commitment__content {
        padding-right: 0 !important;
    }
    .white_bg .commitment__content h4 {
        color: #223852;
        font-size: 40px;
    }

    section.commitment__area.white_bg {
        padding: 0 1em;
    }

    #single-template .exclusive__content h4 {
        font-size: 25px !important;
    }

}


@media (max-width: 999px) {

    /*.accordion-item-jc-unique {*/
    /*    position: relative;*/
    /*    flex: 1 0 20%;*/
    /*    transition: flex 0.3s ease;*/
    /*    overflow: hidden;*/
    /*    height: 100%;*/
    /*    padding:0;*/
    /*    display: flex;*/
    /*    flex-direction: column;*/
    /*    justify-content: flex-end;*/
    /*}*/

    /*#expertise .accordion_inner_jc-unique {*/
    /*    position: relative;*/
    /*    top: 0;*/
    /*    transition: .3s all;*/
    /*    left: 0;*/
    /*}*/

    /*#expertise .accordion-header-jc-unique {*/
    /*    display: block;*/
    /*    text-align: center;*/
    /*    filter: none;*/
    /*    color: #c6cbcd;*/
    /*    padding-top:1em;*/
    /*}*/

    /*#expertise .accordion_bg-unique {*/
    /*    position: relative;*/
    /*    left: 0;*/
    /*    top: 0;*/
    /*    !* height: 100%; *!*/
    /*    width: 100%;*/
    /*}*/

    .header__area {
        padding: 0 0 20px 0;
    }

    #home .exclusive__inner__blk .owl-nav button.owl-next {
        left: 80%;
        position: absolute;
        display: block;
        @media (max-width: 999px) {
            width: 40px;
        }
    }

    #home .exclusive__inner__blk .owl-nav button {
        /*left: 5%;*/
        transition: .3s ease;
        @media (max-width: 999px) {
            width: 40px;
        }
    }

    .video_container {
        background-color: black;
        width: auto;
        /* height: 100vh; */
        display: flex;
        justify-content: center;
        align-items: center;
        margin: auto;
        justify-content: center;
        padding: 0;
    }

    #single-template .exclusive__area {
        padding-bottom: 2em;
    }

    #home .header__area {
        background: #dbe7ef;
        border-bottom: none;
    }

   #home .header__logo {
        transition: .4s all;
        transform: scale(1);
    }

   #home .hero__area {
       height: 45vh !important;
       padding-bottom: 2em;
       padding-top: 2em;
       align-items: center;
   }


    .header__area {

        background: #dbe7ef;
        height: 90px;
    }

    .header__area.sticky {
        height: 90px;
    }

    #single-template section.exclusive__area.bg_image p {
        font-size: 17px;
        padding-top: 0;
        padding-left: 0;
    }

    section.exclusive__area.bg_image .hero__content {
        padding: 0 !important;
    }

    #single-template .exclusive__content__blk p {
        font-size: 18px;
        padding-top: 0;
        padding-left: 1em;
    }


    #home .hero__content {
        padding-bottom: 0;
    }

    .hero__area {
        padding-bottom: 30px;
    }

    .hero__content {
        padding-bottom: 2em;
    }

    .hero__content h5 {
        font-size: 25px;
    }

    .accordion-content-jc {
        padding-left: 10px;
    }

    #home .accordion-item-jc {
        height: 80vh;
    }

    #home .accordion-item-jc-hero {
        padding-top: 40px;
        height: 40vh;
        font-size: 20px !important;
    }

    #home .accordion_inner_jc {
        top: 65%;
    }

    .accordion-jc {
        height: auto;
    }

    .commitment__content {
        margin: auto;
    }

    .commitment__content {
        position: relative;
        left: 0;
    }

    .bg_black .col-lg-7 {
        display: none;
    }

    .bg_black {
        height: auto !important;
        padding: 7em 2em;
        margin-top: 0;
    }

    #home .exclusive__area {
        padding: 0;
    }

    #home .exclusive__area .col-lg-6 {
        padding: 0;
    }

    #home .exclusive__area .hero__content p {
        font-size: 16px;
    }

    .single__studio__contene p {
        font-size: 16px;
        display: block;
    }

    .contact__logo img {
        max-width: 50%;
        margin: auto;
        padding: 0;
        justify-content: center;
        display: flex;
        left: -10px;
        position: relative;
    }

    .contact__logo span {
        font-size: 18px;
        /*color: #232323;*/
        letter-spacing: 18px;
        padding-bottom: 0;
    }

    #home .studio__area {
        margin-top: 7em;
        margin-bottom: 0;
    }

    #home .exclusive__content__blk {
        padding: 0 3em;
    }

    #home .hero__area .hero_logo {
        top: 0;
    }

    #home .contact__area {
        padding-bottom: 5em;
    }

    #home .studio__left__blk {
        max-width: 90%;
        flex-direction: column;
        text-align: left;
        justify-content: center;
        padding-left: 1em;
        padding-right: 1em;
    }

    #home .single__studio__contene h3 {
        font-size: 20px;
        margin-top: 20px;
    }

    #home .single__studio__contene {
        cursor: pointer;
        margin: 25px 0;
    }

    #home .single__studio__contene:nth-child(1) {
        margin-top: 0;
    }

    .footer__area .container {
        max-width: 100%;
        text-align: center;
    }

    .footer__area {
        padding: 50px 0;
    }

    span.__cf_email__ {
        font-size: 16px;
    }

    .footer__social__ico {
        margin: auto;
        justify-content: center;
    }

    .footer__area .mt-40 {
        margin-top: 25px;
    }

    .studio__thumb img {
        object-position: center center;
    }

    .studio__right__blk {
        position: inherit;
        height: 400px;
        width: 100%;
    }

    /*#single-template .accordion-header-jc {*/
    /*    font-size: 50px;*/
    /*    letter-spacing: 25px;*/
    /*}*/
    #single-template .accordion_inner_jc {
        padding-bottom: 0;
        left: 0;
        right:0;
        top: 60%;
        /*top: 60%;*/
        text-align: center;
        margin: auto;
    }
    .exclusive__content h5 {
        font-size: 25px;
        color: #ABACB4;
        font-weight: normal;
    }
    .exclusive__area .container {
        max-width: 100%;
    }
    .exclusive__content__blk {
        max-width: 100%;
        margin: 0 auto;
        padding: 0;
    }
    #single-template .exclusive__content__blk .col-lg-6 {
        justify-content: center;
        display: flex;
    }
    .first_col .col-lg-4 {
        display: flex;
        margin: auto;
        padding: 0 2em !important;
    }
    /*#single-template .exclusive__content__blk p {*/
    /*    padding: 0 1em;*/
    /*}*/
    .box_area {
        max-width: 100%;
        margin-top: 0;
        padding: 0 1em;
    }
    section.exclusive__area.bg_image {
        background-position: left;
        height:900px;
    }
    .bg_image .hero__content {
        padding-bottom: 2em;
        /*padding: 0 1.5em;*/
    }

    #single-template  .commitment__content {
        position: relative;
        padding: 0 1em;
        height: auto;
    }

    #single-template .exclusive__content h4 {
        font-size: 28px;
        margin: 0;
        padding-left: 0;
    }

    #single-template .second h4 {;
        padding-left: 1em;
    }

    #single-template  .commitment__content p {
        font-size: 20px;
    }

    #single-template .commitment__area {
        overflow: hidden;
        height: 90vh;
    }

    .white_bg .commitment__content h4 {
        margin: 0;
    }

    #single-template .commitment__area img {
        max-width: 70%;
        height: auto;
        margin: auto;
        justify-content: center;
        display: flex;
        padding-bottom: 1em;
    }

    .box_area.last {
        padding-top: 0 !important;
    }

    #single-template .accordion_inner_jc {
        padding: 0;
    }

    .header__area .container {
        max-width: 100%;
    }

    header.header__area.sticky {
        padding-top: 0px;
    }

    /*#expertise .accordion-header-jc {*/
    /*    font-size: 50px;*/
    /*    padding-left: 25px;*/
    /*    padding-bottom: 0;*/
    /*}*/

    /*#expertise .accordion_inner_jc {*/
    /*    left: 0;*/
    /*    padding-left: 0;*/
    /*}*/
    .title_studio {
        font-size: 24px;
        padding-left: 0 !important;
        letter-spacing: 10px;
        padding-bottom: 5em;
    }

    #expertise .studio__right__blk {
        position: relative;
        top: 0;
        left: 0;
        width: auto;
        height: auto;
    }

    #expertise .studio__thumb {
        height: auto;
        width: 100%;
    }

    #expertise .contact__area {
        padding-bottom: 5em;
    }

    .accordion-jc-unique {
        display: flex;
        width: 100%;
        cursor: pointer;
        flex-direction: column;
        /*height: 170vh;*/
    }

    .accordion-item-jc-unique.active .accordion_inner_jc-unique {
        top: 55%;
    }

    .accordion-content-jc-unique p {
        margin: 0;
        color: white;
        font-size: 18px;
    }

    .accordion-item-jc-unique.active .accordion-content-jc-unique, .accordion-item-jc-unique:hover .accordion-content-jc-unique {
        padding: 15px 30px 15px 15px;
    }

    .accordion_inner_jc-unique {
        position: absolute;
        top: 70%;
        transition: .3s all;
        left: 10%;
    }

    #expertise .studio__left__blk {
        gap: 60px;
        max-width: 100%;
        padding-right: 1em;
        padding-left: 1em;
        padding-top: 5em;
    }
    #expertise  .exclusive__content__blk {
        padding: 0 2em;
    }

    #expertise  .exclusive__content__blk .col-lg-6 {
        padding: 0;
    }

    #home .accordion_inner_jc {
        left: 20px;
    }

}



@media only screen and (min-width: 768px) and (max-width: 999px) {


    #single-template .accordion-header-jc {
        font-size: 4rem;
        letter-spacing: 40px;
    }

    #home .accordion-header-jc {
        padding-top: 100px;
        font-size: 2rem;
        letter-spacing: 30px;
    }
    #home .accordion-header-jc-hero {
        font-size: 35px;
    }
    /*#expertise .accordion-header-jc {*/
    /*    position: relative;*/
    /*    top: 0;*/
    /*    margin: 0;*/
    /*    left: -7%;*/
    /*}*/

    /*#expertise .accordion-header-jc {*/
    /*    font-size: 3rem !important;*/
    /*}*/

    #single-template .accordion_inner_jc {
        padding-bottom: 0;
        left: -7%;
        top: 75%;
        right: 0;
    }

    .contact__logo img {
        max-width: 35%;
        position: relative;
        padding-top: 6em;
    }

    .point img {
        flex-basis: 60%;
        max-width: 60%;
        height: 600px;
        object-fit: cover;
        border-radius: 0;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transform: translateY(0px);
    }
    .point article {
        padding-top: 0;
    }

    .scroll_sticky .point {
        padding: 0;
    }
    .scroll_sticky .points {
        position: relative;
        width: 100%;
        height: 70vh;
        overflow: visible;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 30px;
    }


    .point h3 {
        color: #5a6c76;
        font-weight: 900;
        font-size: 20px;
        font-family: 'picadilly', sans-serif;
    }

    /*#expertise .accordion-header-jc {*/
    /*    font-size: 70px !important;*/
    /*}*/

    .scroll_sticky {
        position: relative;
        padding: 0;
    }

    .point p {
        color: #ABACB4;
        font-size: 12px;
    }


    .scroll_sticky .point {
        transform: translateY(200px);
    }

    .point article {
        padding-left: 2em;
    }

    .principle_section .text h2 {
        color: #223852;
        font-size: 40px;
    }

    .principle_section .text {
        padding-left: 2em;
    }

    .principle_section .text p {
        color: #ABACB4;
        font-size: 21px;
    }

    .single__studio__contene p {
        font-size: 20px !important;
        display: block;
        width: 100% !important;
    }

    #single-template .commitment__area img {
        max-width: 50%;
    }

    #single-template .accordion-header-jc {
        position: relative;
        top: -80px;
    }

    .exclusive__area.second  {
        padding: 8em 0;
        padding-left: 1em;
    }
    #single-template .accordion-item-jc {
        position: relative;
        height: 40vh;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        text-align: left;
    }
    #home .accordion-item-jc {
        height: 40vh;
    }
    #home .accordion-item-jc-hero {
        padding-top: 40px;
        height: 30vh;
        font-size: 20px !important;
    }

    #single-template .accordion_inner_jc {
        padding-bottom: 0;
        left: 0;
        top: 80%;
    }
    .studio__left__blk {
        gap: 50px;
    }
    .accordion-content-jc {
        padding-left: 0;
    }
    .header__area.sticky .header__logo a img {
        height: 80px;
        margin-top: 80px;
        padding-left: 40px;
    }
}
@media only screen and (min-width: 768px) and (max-width: 1199px) {
    .single__studio__contene p {
        font-size: 16px;
        display: block;
        width: 40%;
    }


    .bg_black  .col-lg-3 {
        width: 100%;
    }

    .exclusive__inner__blk .owl-nav button {
        left: 12%;
        height: 70px;
        width: 70px;
    }

    .studio__thumb img {
        height: 100%;
        width: 100%;
        object-fit: contain;
        object-position: right;
    }
}

@media only screen and (min-width: 1000px) and (max-width: 1199px) {
    .bg_black .col-lg-3 {
        width: 30%;
    }

    .principle_section {
        padding: 10em 2em;
    }

    .principle_section .text h2 {
        color: #223852;
        font-size: 40px;
    }

    .principle_section .text {
        padding-left: 2em;
    }

    .principle_section .text p {
        color: #ABACB4;
        font-size: 21px;
    }
    .image__grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(4, auto);
    }
}

@media only screen and (min-width: 1000px) and (max-width: 1499px) {
    .exclusive__content__blk  .row.g-4 {
        justify-content: center;
    }
    /*#expertise .accordion-header-jc {*/
    /*    font-size: 60px;*/
    /*    letter-spacing: 50px;*/
    /*}*/

    /*#home .hero__content h4 {*/
    /*    font-size: 60px;*/
    /*    letter-spacing: 30px;*/
    /*}*/
}

@media (min-width: 1499px) {
    #single-template .accordion_inner_jc {
        padding-bottom: 0;
        left: 2%;
        text-align: center;
        width: 100%;
        right: 0;
        margin: auto;
    }


    /*#expertise .accordion_inner_jc {*/
    /*    padding-bottom: 0;*/
    /*    left: 1%;*/
    /*    text-align: center;*/
    /*    width: 100%;*/
    /*    top: 60%;*/
    /*    right: 0;*/
    /*    margin: auto;*/
    /*    padding: 0;*/
    /*}*/

    /*#expertise .accordion-item-jc {*/
    /*    height: 70vh;*/
    /*}*/

    /*#expertise .accordion-header-jc {*/
    /*    padding-left:0;*/
    /*    padding-right: 0;*/
    /*}*/


    /*#expertise .accordion-header-jc {*/
    /*    font-size: 10rem !important;*/
    /*}*/

    #single-template .accordion-header-jc {
        font-size: 8rem;
        font-weight: bold;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 80px;
        text-align: center;
        color: #eff3f7;
        left: 0;
        right: 0;
    }
    #home .accordion-header-jc {
        font-size: 4rem;
        text-transform: uppercase;
        padding-top: 100px;
        letter-spacing: 50px;
    }
    #home .accordion-header-jc-hero {
        font-size: 90px;
    }
}
@media (max-width: 1024px) {

    .header__logo a img {
        height: auto;
        max-width: 250px;
        margin-top: 75px;
    }

    .accordion_inner_jc {
        bottom: 10px;
        left: 40px;
        max-width: 80%; /* More space for smaller screens */
    }
    .accordion-header-jc {
        font-size: 3rem;
    }
    .accordion-content-jc p {
        font-size: 1.2rem;
    }
    .accordion-header-jc {
        font-size: 2.5rem;
        letter-spacing: 3px;
    }
    .accordion-content-jc p {
        font-size: 1rem;
        line-height: 1.3;
    }
    .accordion_inner_jc {
        bottom: 20px;
        left: 20px;
        max-width: 90%;
    }


}

@media only screen and (min-width: 1000px) and (max-width: 1138px) {
    .header__btn a {
        padding: 25px 25px;
    }
    .contact__btn__nav {
        padding: 0 !important;
        margin: 0 20px;
    }
    .accordion-content-jc p {
        font-size: 25px !important;
        line-height: 1.5;
    }
    .accordion_inner_jc {
        max-width: 100%;
    }

}

@media only screen and (min-width: 1139px) and (max-width: 1399px) {
    .accordion_inner_jc {
        max-width: 60%;
    }
    #single-template .accordion_inner_jc {
        max-width:100% !important;
    }

}

@media (max-width: 480px) {
    .accordion-header-jc {
        font-size: 2rem;
    }
    .accordion-content-jc p {
        font-size: 0.9rem;
        text-align: left;
    }
    .accordion_inner_jc {
        bottom: 10px;
        left: 15px;
        max-width: 95%;
    }
}

@media (min-width: 1700px) {
    #home section.commitment__area.bg_black {
        margin-bottom: 5em;
        height: 100vh;
    }
    .exclusive__area .container {
        max-width: 100%;
    }
    #home .exclusive__inner__blk .owl-nav button {
        left: 9%;
    }
    #home .exclusive__inner__blk .owl-nav button.owl-next {
        left: 87%;
        position: absolute;
        display: block;
    }
    .studio__area .container {
        max-width: 1500px;
    }
}
@media (min-width: 2000px) {
    #home .accordion_inner_jc {
        top: 65%;
        left: 12%;
        max-width: 50%;
    }
}

@media only screen and (max-width: 1500px) and (min-width: 1000px) {
    #home .accordion_inner_jc {
        left: 10px;
    }

    #single-template .accordion-header-jc {
        font-size: 4rem;
        padding-left: 0;
        letter-spacing: 70px;
    }

    #home .accordion-header-jc {
        padding-top: 100px;
        font-size: 3rem;
        padding-left: 0;
        letter-spacing: 50px;
    }
    #home .accordion-header-jc-hero {
        font-size: 70px;
    }

    #single-template .accordion_inner_jc {
        padding-bottom: 0;
        left: 5%;
        text-align: center;
        width: 100%;
        /*top: 75%;*/
        right: 0;
        margin: auto;
    }

    .header__btn {
        margin-left: 0;
    }

    .header__btn a {
        padding: 25px 5px;
    }

   .contact__btn__nav {
       padding: 0 !important;
       margin: 0 5px;
    }

    #home .accordion_inner_jc {
        max-width: 75%;
    }

    .header__inner__blk {
        padding: 0 10px;
    }

    #single-template .box_area {
        max-width: 90%;
        padding-top: 0;
        margin-top: 0;
    }

    .header__area .container {
        max-width: 85%;
    }

}
@media only screen and (max-width: 1200px) and (min-width: 1000px) {
    .header__btn a {
        padding: 25px 20px;
    }
    .contact__btn__nav {
        padding: 0 !important;
        margin: 0 20px;
    }
}

@media (max-width: 767px) {
    .home_intro_section{
        width: 100%;
        margin: 30px auto;
    }

    #home .accordion-item-jc-unique.active .accordion_inner_jc-unique {
        left: 0;
        top: 25%;
        padding: 0 3em;
    }
    #home .accordion-item-jc-unique.active .accordion-content-jc, .accordion-item-jc-unique:hover .accordion-content-jc {
        max-height: none;
    }

    .home_intro_section h2 {
        font-size: 20px;
    }
    .article__links__text a {
        font-size: 12px !important;
    }

    /*#expertise .accordion-header-jc {*/
    /*    font-size:30px;*/
    /*}*/

    #single-template .accordion-header-jc {
        font-size: 2rem;
        letter-spacing: 25px;
        left: 0;
        right: 0;
        margin: auto;
        padding-left: 0;
    }

    #home .accordion-header-jc {
        padding-top: 50px;
    }

    #home .accordion-item-jc {
        height: 20vh;
        padding-top: 50px;
    }

    #home .accordion-item-jc-hero {
        padding-top: 0;
        height: 100vh;
        font-size: 18px !important;
    }

    #single-template .accordion_inner_jc {
        padding: 0;
        right: 0;
        left: 5%;
        position: absolute;
        /*top: 80%;*/
    }

    /*#expertise .accordion_inner_jc {*/
    /*    padding: 0;*/
    /*    right: 0;*/
    /*    left: 5%;*/
    /*    position: absolute;*/
    /*    top: 75%;*/
    /*}*/

    footer.footer__area {
        background: url(../images/sturge-toth-crest-white.svg) no-repeat, url(../images/sturge-toth-crest-white.svg) no-repeat;
        background-color: #f3f3f2;
        background-position: -27% center, 130% center;
        background-size: 50%;
        left: 0;
        right: 0;
        position: absolute;
        background-repeat: no-repeat;
    }

    .mobile_title {
        padding-bottom: 2em;
        padding-top: 1em;
    }

    .mobile_title.font-70 {
      font-size: 25px;
        padding-bottom: .5em;
        padding-top: .5em;
    }

    /*#expertise header.header__area.sticky {*/
   /*     display: none;*/
   /* }*/

    .point img {
        height: 200px;
        object-fit: cover;
    }

    .scroll_sticky .point {
        position: absolute;
        height: 50vh;
    }
    .footer_bottom p {
        font-size: 14px;
    }

    section.exclusive__area.bg_image {
        background-attachment: initial;
    }

   #expertise .exclusive__area {
        padding: 4em 0;
    }

    .point p {
        color: #ABACB4;
        font-size: 14px;
    }

    .point h3 {
        font-size: 18px;
    }

    .accordion-header-jc-unique {
        font-size: 35px;
        filter: drop-shadow(2px 4px 6px black);
    }

    .bg_image .exclusive__content__blk {
        padding: 1em;
    }

    .scroll_sticky .point {
       flex-direction: column;
        padding: 0;
    }

    #home .single__studio__contene h3 {
        letter-spacing: 15px;
    }

    #single-template .accordion-item-jc {
        position: relative;
        height: 30vh;
    }


    /*#expertise .accordion-jc {*/
    /*    height: 40vh;*/
    /*}*/

    #expertise .exclusive__area {
        padding-bottom: 5em;
    }

    .point article {
        padding-left: 0;
        padding-top: 2em;
    }

    .point article {
        padding-left: 0;
    }

    .point img {
        flex-basis: 50%;
        max-width: 100%;
        transform: translateY(0);
    }

}

@media only screen and (max-width: 1199px) and (min-width: 1000px) {
    .header__btn a {
        font-size: 12px;
    }

    /*#expertise .accordion-header-jc {*/
    /*    font-size: 3rem;*/
    /*}*/
}


@media only screen and (max-width: 1400px) and (min-width: 1000px) {

    .crafted__design__text{
        margin-left: 0;
        margin-right: 0;
        max-width: 50%;
    }

    .point p {
        color: #ABACB4;
        font-size: 12px;
    }

    .point h3 {
        color: #5a6c76;
        font-weight: 900;
        font-size: 25px;
        font-family: 'picadilly', sans-serif;
    }

    .scroll_sticky .point {
        height: 60vh;
    }

    .title_studio {
        font-size: 35px;
        font-family: 'picadilly', sans-serif;
        color: #eff1f2;
        align-self: baseline;
        margin: 0;
        padding-left: 1em;
        text-transform: uppercase;
        letter-spacing: 40px;
        padding-bottom: 1em;
        padding-top: 2em;
    }
    .crafted__design__text{
        margin-left: 160px;
        max-width: 50%;
    }

}