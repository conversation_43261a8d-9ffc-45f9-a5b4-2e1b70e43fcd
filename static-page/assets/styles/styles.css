/* Base CSS */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

.studio__thumb {
    display: none;
}
.studio__thumb.active {
    display: block;
}

p, strong {
    text-align: justify;
}

.ml-100 {
    margin-left: 100px;
    /*@media (max-width: 1200px) {*/
    /*    margin-left: 40px;*/
    /*}*/
    @media (max-width: 999px) {
        margin-left: auto;
    }
}

.z-1 {
    z-index: 1;
}

.flex-row {
    display: flex;
    flex-direction: row;
}

.lg-flex-row {
    display: flex;
    flex-direction: row !important;
    @media (max-width: 999px) {
        flex-direction: column !important;
    }
}

.video_container {
    background-color: black;
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    @media (max-width: 999px) {
        height: 100%;
    }
}

video.hero-video {
    width: 100%;
    @media (max-width: 999px) {
        height: 400px;
    }
}

.pt-100 {
    @media (max-width: 999px) {
        padding-top: 0;
    }
}

.hidden-paragraph {
    display: none; /* Hidden by default */
}

.toggle-read-more {
    color: #ABACB4;
    cursor: pointer;
    text-decoration: underline;
    margin-top: 10px;
    display: inline-block;
    font-size: 14px;
}

.read-more {
    color: #5a6c76;
    cursor: pointer;
    text-decoration: underline;
    margin-top: 10px;
    display: inline-block;
    font-size: 18px;
}

.pr-40 {
    padding-right: 40px;
    @media (max-width: 999px) {
        padding-right: 0;
    }
}

.no-pt {
    padding-top: 0 !important;
}

.py-120 {
    padding: 120px 0;
    @media (max-width: 999px) {
    padding: 40px 0;
}
}

.beige-bg {
    background-color: #f3f3f2;
}

.text-left {
    text-align: left !important;
}

.text-80px {
    font-size: 80px !important;
}

.alignleft {
    float: left;
    margin-right: 15px;
}

.alignright {
    float: right;
    margin-left: 15px;
}

.aligncenter {
    display: block;
    margin: 0 auto 15px;
}

a:focus {
    outline: 0 solid
}

img {
    max-width: 100%;
    height: auto;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0 0 15px;
    color: #07171D;
    font-weight: 700;
}

h1, h2. h3 {
    font-family: 'picadilly', sans-serif;
}

body {
    color: #07171D;
    font-weight: 400;
    font-family: 'poppins', sans-serif;
}

.selector-for-some-widget {
    box-sizing: content-box;
}

li {
    list-style: none;
}

a {
    text-decoration: none;
    font-weight: 400;
}

ul {
    margin: 0;
    padding: 0;
}

a:hover {
    text-decoration: none
}

a,
button,
input,
textarea {
    outline: none !important;
}

.section-padding {
    padding: 80px 0;
}

p {
    font-weight: 200;
    font-family: 'poppins-light', sans-serif;
}

p b {
    font-weight: 900;
}

.lg-w-50 {
    width: 70%;
    @media (min-width: 1024px) and (max-width: 1200px ) {
        width: 100%;
    }
    @media (max-width: 1023px ) {
        width: 100%;
    }
}


/* ----------------------------------------- HEADER AREA START ------------------------ */

.header__top__content p {
    margin: 0;
    color: #FFF;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
}

.header__top__area {
    background: #000000;
    padding: 5px 0;
}

.main__menu ul li {
    display: inline-block;
    margin-left: 50px;
    position: relative;
}

.header__btn {
    margin-left: 67px;
}

.main__menu ul li a {
    color: #000;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    display: inline-block;
    padding: 26px 0;
    transition: .3s all;
}

.social__ico__blk {
    display: flex;
    gap: 8px;
    margin-left: 30px;
}

.header__area .container {
    max-width: 1500px;
}

.header__btn a {
    color: #ffffff;
    font-size: 16px;
    font-style: normal;
    font-weight: 900;
    line-height: normal;
    transition: .3s all;
    /* background: #D4E5FA; */
    padding: 25px 30px;
    display: inline-flex;
    align-items: center;
    /*font-family: 'picadillyThin', sans-serif;*/
    letter-spacing: 3px;
}

.header__btn a:hover {
    color: #3170B0;
}

.header__inner__blk {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    height: 60px;
}

.header__logo a {
    display: inline-block;
}

.header__logo a img {
    height: auto;
    opacity: 1;
    margin-top: 3em;
    width: 70%;
}

.header__video__logo a img {
    height: auto;
    opacity: 1;
    margin-top: 3em;
    width: 70%;
}

.header__area {
    padding: 10px 0;
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    z-index: 555;
    background: #dbe7ef;
    height: 130px;
    border-bottom: 1px solid #fff;
}

#home header.header__area.sticky {
    background: #dbe7ef !important;
}

#home .header__area.sticky .header__btn a {
    color: #ffffff;
}

#home .header__area {
    background: #fff;
    border-bottom: none;
}

#home .header__btn a {
    color: #021712;
}

.header__area.sticky {
    background: #dbe7ef;
}

.header__right__blk {
    display: flex;
    align-items: center;
}

.mega__menu {
    background: #FFF;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
    padding: 22px 30px;
    display: flex;
    width: 582px;
    gap: 10px;
    transition: .3s all;
    position: absolute;
    top: 130%;
    visibility: hidden;
    opacity: 0;
    left: 0;
    z-index: 333;
}

.mega__menu__left {
    width: 225px;
    flex: 0 0 auto;
}

.mega__menu__left h4 {
    color: #000;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.main__menu .main__menu__link a {
    padding: 0;
    color: #000;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    display: block;
    margin-top: 3px;
    transition: .3s all;
}

.mega_menu_right p {
    color: #000;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin: 0;
}

.header__right__blk ul li:hover > a {
    color: #3170B0;
}

.main__menu ul li:hover .mega__menu {
    visibility: visible;
    opacity: 1;
    top: 100%;
}

.main__menu .main__menu__link a:hover {
    color: #3170B0;
}

.header__burger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.header__burger span {
    width: 30px;
    height: 3px;
    background: #07171D;
    margin: 4px 0;
    transition: 0.3s;
}

.header__nav {
    display: flex;
    flex-direction: row;
}


.header__burger .line {
    width: 30px;
    height: 3px;
    background: #fff; /* Adjust color as needed */
    margin: 4px 0;
    transition: 0.3s;
}

.header__nav__close {
    display: none;
    font-size: 35px;
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    color: #fff;
}

.header__nav a {
    margin: 0 10px;
}

.sea_land_air_mob_text {
    font-size: 14px !important;
    letter-spacing: 12px !important;
}

@media (max-width: 1029px) {
    .desktop {
        display: none;
    }
}

@media (min-width: 1030px) {
    .mobile {
        display: none;
    }
}

@media (max-width: 1029px) {
    .header__nav {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 70px;
        left: 0;
        width: 100%;
        background: #dbe7ef;
    }

    .header__nav a {
        margin: 5px 15px;
        color: #6c757d;
        letter-spacing: 1px;
        /*font-weight: 900;*/
        font-family: 'poppins', sans-serif;
        text-align: center;
        padding-bottom: 1em;
    }

    .header__nav.active {
        display: flex;
        text-align: right;
        padding-right: 0;
        padding-top: 0;
        font-size: 29px;
        padding-bottom: 1em;
        height: 100vh;
        top: 0;
        justify-content: center;
    }

    header.header__area.sticky .header__nav.active {
        margin-top: 1em;
    }

    .header__nav__close {
        display: block;
        z-index: 10;
        position: relative;
        top: 0;
        right: 1em;
        bottom: 0;
        font-size: 55px;
    }

    .header__burger {
        display: flex;
        align-items: flex-end;
        padding-right: 1em;
        margin-top: 5em;
        padding-bottom: 10px;
    }
}

/* ----------------------------------------- HEADER AREA END ------------------------ */


/* ----------------------------------------- BANNER AREA START ------------------------ */

.banner__area {
    height: 350px;
    display: flex;
    align-items: flex-end;
    background: #223852;
}

.banner__area .container {
    display: flex;
    height: 100%;
    padding-top: 70px;
}

.banner__content {
    text-align: center;
    margin: 0 auto;
    gap: 30px;
}

.banner__content img {
    max-width: 204px;
}

.banner__content span {
    font-size: 21px;
    color: #fff;
    letter-spacing: 21px;
}

.banner__content span:last-of-type {
    font-size: 18px;
    letter-spacing: 6.3px;
}

/* ----------------------------------------- BANNER AREA END ------------------------ */

/* ----------------------------------------- HERO AREA START ------------------------ */

.hero__area {
    height: 45vh;
    display: flex;
    align-items: center;
    padding: 50px 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background: #dbe7ef;
}

#home .hero__area {
    display: flex;
    align-items: flex-end;
    background: #fff;
    height: auto;
    padding-top: 6em;
    padding-bottom: 6em;
}


.hero__area .container {
    max-width: 1600px;
}

.hero__content h1 {
    font-size: 46px;
    color: #212121;
    font-weight: 700;
}

.hero__content h4 {
    font-size: 25px;
    font-weight: 900;
}

.hero__content h5 {
    color: #ABACB4;
    margin: 0 20px 0 0;
    font-weight: 300;
    font-size: 25px;
}

.hero__content p {
    color: #ABACB4;
    font-size: 16px;
    padding-top: 10px;
    margin: auto;
}

.hero__content {
    padding: 1em
}

#founders .hero__content {
    padding: 0;
}

.hero__inner__blk {
    padding: 40px;
    background: transparent;
    border-radius: 5px;
    /*! box-shadow: 0 4px 30px rgba(0, 0, 0, 0.13); */
    /*! border: 1px solid rgba(32, 32, 32, 0.44); */
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding-top: 10em;
    transition: .3s all;
    position: relative;
    overflow: hidden;
}

#home .hero__inner__blk {
    padding-top: 0;
}

.header__area.sticky {
    z-index: 444;
    /*! -webkit-animation: 500ms ease-in-out 0s normal none 1 running fadeInDown; */
    /*! animation: 500ms ease-in-out 0s normal none 1 running fadeInDown; */
    top: 0;
    -webkit-box-shadow: 0 8px 20px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 8px 20px 0 rgba(0, 0, 0, .1);
    height: 130px;
}

.hero_logo img {
    height: 55px;
}

header.header__area.sticky .header__btn {
    margin-top: 3em;
}

.hero_logo.sticky {
    transform: scalex(0);
}

.hero_logo {
    transition: .3s all;
    position: absolute;
    top: 20px;
}

.header__logo {
    transition: .4s all;
    transform: scale(0);
}

.header__area.sticky .header__logo {
    transform: scale(1);
}

.hero__inner__blk.sticky {
    padding-top: 40px;
}

.hero__inner__blk.sticky .hero_logo {
    top: -40px;
    visibility: hidden;
    opacity: 0;
}

/* ----------------------------------------- HERO AREA END ------------------------ */


/* ----------------------------------------- ACCORDION AREA START ------------------------ */
.accordion__area {
    position: relative;
    overflow: hidden;
}

#home .accordion-item-jc {
    position: relative;
    height: 115vh;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    text-align: left;
}


#home .accordion-item-jc-hero {
    position: relative;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin: auto;
}

/*#expertise .accordion-item-jc {*/
/*    position: relative;*/
/*    flex: 1 0 20%;*/
/*    !* Default width for each item *!*/
/*    transition: flex 0.3s ease;*/
/*    overflow: hidden;*/
/*    height: 60vh;*/
/*    padding: 50px 100px;*/
/*    display: flex;*/
/*    flex-direction: column;*/
/*    justify-content: flex-end;*/
/*    text-transform: uppercase;*/
/*    text-align: center;*/
/*}*/

#single-template .accordion-item-jc {
    position: relative;
    height: 50vh;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    text-align: left;
}

.accordion_bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    width: 100%;
    height: 100%;
    z-index: -1;
}

div#jarallax-container-0 div {
    background-position: 50% 15% !important;
    width: 100% !important;
    height: 100% !important;
}

.accordion_inner_jc {
    position: absolute;
    bottom: 20px;
    left: 70px;
    color: white;
    z-index: 1;
    padding: 1em;
    max-width: 45%;
}

#home .accordion_inner_jc {
    left: 105px;
}

#single-template .accordion_inner_jc {
    padding-bottom: 0;
    left: 0;
    text-align: center;
    width: 100%;
    top: 40%;
}

.accordion-header-jc {
    font-size: 50px;
    font-weight: bold;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 5px;
    padding-bottom: 10px;
    font-family: 'picadilly', sans-serif;
}

#home .accordion-header-jc-hero {
    margin: auto;
    padding-top: 100px;
    font-size: 100px;
    font-weight: bold;
    text-transform: uppercase;
    padding-bottom: 10px;
    font-family: 'picadilly', sans-serif;
    color: #eff3f7;
    max-height: 40vh;
    letter-spacing: 2rem;
}


#home .accordion-header-jc {
    font-size: 350px;
    padding-top: 200px;
    font-weight: bold;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 2px;
    padding-bottom: 10px;
    font-family: 'picadilly', sans-serif;
}

/*#expertise .accordion-header-jc {*/
/*    font-size: 150px;*/
/*    font-weight: bold;*/
/*    margin-bottom: 10px;*/
/*    text-transform: uppercase; !important;*/
/*    letter-spacing: 70px;*/
/*    text-align: center;*/
/*    color: #fff;*/
/*    width: 100%;*/
/*    display: block;*/
/*    white-space: nowrap;*/
/*    overflow: hidden;*/
/*    box-sizing: border-box;*/
/*}*/


#single-template .accordion-header-jc {
    font-size: 150px;
    font-weight: bold;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 80px;
    text-align: center;
    color: #eff3f7;
}

.accordion-content-jc p {
    font-size: 25px;
    line-height: 1.5;
}

.accordion__item__1 .accordion_bg {
    background: linear-gradient(180deg, rgba(34, 56, 82, 0) 0%, #223852 100%), url('../images/SEA.jpg') no-repeat center center;
}

.accordion__item__2 .accordion_bg {
    background: linear-gradient(180deg, rgba(34, 56, 82, 0) 0%, #223852 100%), url('../images/LAND.jpg') no-repeat center center;
}

.accordion__item__3 .accordion_bg {
    background: linear-gradient(180deg, rgba(34, 56, 82, 0) 0%, #223852 100%), url('../images/AIR.jpg') no-repeat center center;
}

.accordion__item__4 .accordion_bg {
    background: #dbe7ef;
    background-size: cover !important;
}

.accordion__item__5 .accordion_bg {
    background: linear-gradient(180deg, rgba(34, 56, 82, 0) 30%, #223852 100%), url('../images/expertise.png') no-repeat center center !important;
    background-size: cover !important;
}

#single-template div#jarallax-container-0 div {
    background-image: none !important;
}

#expertise div#jarallax-container-0 div {
    background-image: none !important;
}

.accordion__item__1 .accordion_bg {
    background-position: center top;
}

.accordion__item__2 .accordion_bg {
    background-position: center bottom;
}

.accordion__item__3 .accordion_bg {
    background-position: center;
}

/* Media Queries for smaller screens */
@media (max-width: 768px) {
    .accordion-header-jc {
        font-size: 2rem;
    }

    .accordion-content-jc p {
        font-size: 1rem;
    }
}


/* ----------------------------------------- ACCORDION AREA END ------------------------ */


/* ----------------------------------------- ACCORDION AREA START ------------------------ */

/*#expertise .accordion-jc {*/
/*    display: flex;*/
/*    width: 100%;*/
/*    cursor: pointer;*/
/*    height: 85vh;*/
/*    @media (max-width: 999px) {*/
/*    height: 70vh;*/
/*    }*/
/*}*/

/*#expertise .accordion-item-jc {*/
/*    position: relative;*/
/*    flex: 1 0 20%;*/
/*    !* Default width for each item *!*/
/*    transition: flex 0.3s ease;*/
/*    overflow: hidden;*/
/*    height: 60vh;*/
/*    padding: 50px 100px;*/
/*    display: flex;*/
/*    flex-direction: column;*/
/*    justify-content: flex-end;*/
/*    text-transform: uppercase;*/
/*    text-align: center;*/
/*}*/

#expertise .exclusive__content__blk {
    max-width: 100%;
    text-align: center;
}

/*#expertise .accordion-header-jc {*/
/*    color: #fff;*/
/*    padding: 15px;*/
/*    font-size: 100px;*/
/*    padding-bottom: 40px;*/
/*    line-height: normal;*/
/*    text-transform: inherit;*/
/*    padding-left: 1em;*/
/*}*/

#expertise .accordion__area {
    overflow: hidden;
}

#expertise.accordion_bg {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
}

.accordion_bg img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: .3s all;
    filter: grayscale(1);
}

.accordion-item-jc.active .accordion_bg img {
    filter: grayscale(0);
}

.studio__area .container,
.studio__area .row,
.studio__area .col-xl-6 {
    height: 100%;
}

.accordion-content-jc {
    padding: 15px;
    max-height: 0;
    transition: max-height 0.3s ease, padding 0.3s ease;
    font-size: 21px;
    color: #fff;
    opacity: 0;
    visibility: hidden;
    padding-left: 0;
}

#home .accordion-content-jc {
    visibility: visible;
    opacity: 1;
    color: white;
    padding-left:0;
}

.accordion-item-jc-unique {
    transition: flex 0.3s ease, padding 0.3s ease, opacity 0.3s ease, max-height 0.3s ease, visibility 0.3s ease;
    /* Add transitions to the accordion item for smooth effects */
}

.accordion-item-jc-unique.active,
.accordion-item-jc-unique:hover {
    flex: 1 0 60%;
    /* Expanded item to 60% */
}

.accordion-item-jc-unique:not(:hover):not(.active) {
    flex: 1 0 20%;
    /* Non-hovered items take 20% */
}

.accordion-item-jc-unique.active .accordion-content-jc,
.accordion-item-jc-unique:hover .accordion-content-jc {
    max-height: 200px;
    /* Set a max height for the expanded content */
    padding: 15px 100px 15px 15px;
    /* Show padding when expanded */
    opacity: 1;
    visibility: visible;
    transition: max-height 0.3s ease, padding 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
    /* Add transition for content expansion */
}

.accordion-item-jc-unique .accordion-content-jc {
    max-height: 0; /* Initially set to 0 for non-expanded items */
    opacity: 0;
    visibility: hidden;
    overflow: hidden; /* Hide the content */
    transition: max-height 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
}

.accordion-content-jc p {
    margin: 0;
    color: #fff;
}

.accordion_inner_jc {
    position: absolute;
    top: 60%;
    transition: .3s all;
    bottom: 16px;
    left: 0;
    color: #eff3f7;
    z-index: 1;
    padding: 1em;
    max-width: 100%;
}

/*#home .accordion_inner_jc {*/
/*    top: 50%;*/
/*}*/

/*#expertise .accordion_inner_jc {*/
/*    position: absolute;*/
/*    transition: .3s all;*/
/*    bottom: auto;*/
/*    top: auto;*/
/*    color: white;*/
/*    z-index: 1;*/
/*    padding: 1em;*/
/*    max-width: 100%;*/
/*    left: 0;*/
/*    padding-left: 0;*/
/*    width: 100%;*/
/*}*/

.accordion-item-jc.active .accordion_inner_jc {
    top: auto;
}

.single__studio__contene.active h3 {
    color: #232323;
}


.accordion-jc-unique {
    display: flex;
    width: 100%;
    cursor: pointer;
    height: 100vh;
}

/*#expertise .accordion-jc-unique {*/
/*    height: 70vh;*/
/*}*/

.accordion-item-jc-unique {
    position: relative;
    flex: 1 0 20%;
    /* Default width for each item */
    transition: flex 0.3s ease;
    overflow: hidden;
    height: 100%;
    padding: 50px 100px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.accordion-header-jc-unique {
    font-size: 50px;
    text-align: left;
    font-weight: bold;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 5px;
    padding-bottom: 10px;
    font-family: 'picadilly', sans-serif;
    color: #fff;
}

/*.accordion-header-jc-unique.mb {*/
/*    margin-bottom: 200px;*/
/*}*/

.accordion__area-unique {
    overflow: hidden;
}

.accordion_bg-unique {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
}

.accordion_bg-unique img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: 1.5s all;
    filter: grayscale(1);
}

.accordion-item-jc-unique.active .accordion_bg-unique img {
    filter: grayscale(0);
}

.accordion-item-jc-unique:hover .accordion_bg-unique img {
    filter: grayscale(0);
}

.studio__area-unique .container,
.studio__area-unique .row,
.studio__area-unique .col-xl-6 {
    height: 100%;
}

.accordion-content-jc-unique {
    padding: 15px;
    max-height: 0;
    transition: max-height 0.3s ease, padding 0.3s ease;
    font-size: 21px;
    color: #fff;
    opacity: 0;
    visibility: hidden;
    padding-left: 0 !important;
}

.accordion-item-jc-unique.active,
.accordion-item-jc-unique:hover {
    flex: 1 0 60%;
    /* Expanded item to 60% */
}

.accordion-item-jc-unique:not(:hover):not(.active) {
    flex: 1 0 20%;
    /* Non-hovered items take 20% */
}

.accordion-item-jc-unique.active .accordion-content-jc-unique,
.accordion-item-jc-unique:hover .accordion-content-jc-unique {
    max-height: 200px;
    /* Set a max height for the expanded content */
    padding: 15px 100px 15px 15px;
    /* Show padding when expanded */
    opacity: 1;
    visibility: visible;
}

.accordion-content-jc-unique p {
    margin: 0;
    color: white;
    font-size: 20px;
}

.accordion_inner_jc-unique {
    position: absolute;
    top: 88%;
    transition: .3s all;
}

#expertise .accordion_inner_jc-unique {
    position: absolute;
    top: 50%;
    transition: .3s all;
}

.accordion-item-jc-unique.active .accordion_inner_jc-unique {
    top: 45%;
    /*left: 35%;*/
}

#home .accordion-item-jc-unique.active .accordion_inner_jc-unique {
    top: 44%;
    left: 10%;
}

#home .accordion-content-jc p {
    margin-top: 10px;
}

.single__studio__content-unique.active h3 {
    color: #232323;
}

.accordion-header-jc-unique {
    display: none; /* Hide by default */
}

.accordion-item-jc-unique.active .accordion-header-jc-unique,
.accordion-item-jc-unique:hover .accordion-header-jc-unique {
    display: block; /* Show only on hovered or active items */
}

/* ----------------------------------------- COMMITMENT AREA START ------------------------ */
.commitment__logo {
    padding: 12px;
    background: #ABACB4;
    height: 100%;
    display: flex;
    justify-content: center;
}

#home section.commitment__area.bg_black {
    margin-bottom: 5em;
    height: 115vh;
    margin-top: 2em;
}

.commitment__logo {
    padding: 12px;
    background: #F7F5F2;
    height: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: relative;
    z-index: 1;
}

.commitment__area {
    overflow: hidden;
    height: 100vh;
}


#single-template .commitment__area {
    overflow: hidden;
    height: 80vh;
}

#single-template .commitment__area .col-lg-5 {
    display: flex;
    justify-content: center;
    align-items: center;
}

#single-template .commitment__area img {
    max-width: 85%;
    height: auto;
    margin: auto;
    justify-content: center;
    display: flex;
}


.bg_black {
    background: #000000;
}

.white_bg {
    background: #ffffff;
}

.white_bg .commitment__content {
    align-items: baseline;
}

.white_bg .commitment__content h4 {
    color: #223852;
    font-size: 55px;
    font-family: 'picadilly', sans-serif;
    text-align: left;
    max-width: 100%;
    padding-left: 0;
    padding-bottom: 30px;
}

.white_bg p {
    color: #ABACB4;
    font-size: 28px;
}

.commitment__area .container,
.commitment__area .row,
.commitment__area .col-lg-6 {
    height: 100%;
}

.commitment__content {
    position: relative;
    left: 5em;
}

.commitment__content h4 {
    margin: 0;
    font-size: 35px;
    max-width: 490px;
    color: #363636;
    padding: 12px;
    text-align: center;
}

.commitment__content img {
    padding-bottom: 2em;
    width: 80%;
}

.commitment__content h4 span {
    color: #fff;
}

.commitment__content {
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
}

.commitment__logo:before {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 8000px;
    content: '';
    background: #F7F5F2;
    z-index: -1;
}

.commitment__logo a img {
    height: 300px;
}

/* ----------------------------------------- COMMITMENT AREA END ------------------------ */


/* ----------------------------------------- EXCLUSIVE AREA START ------------------------ */
.exclusive__area {
    padding-top: 4em;
}

.exclusive__area.pt {
    padding-top: 10em;
}

#founders.pb {
    padding-bottom: 10em;
}

section.exclusive__area.bg_image {
    /*margin-top: 3em;*/
    height: 1000px;
    position: relative;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0, rgba(0, 0, 0, 1) 100%), url('../images/ben.png') 0 0 no-repeat padding-box;
    background-size: cover;
    background-attachment: fixed;
    background-position: center;
    overflow: hidden;
}

.bg_image .col-lg-6 {
    padding: 0;
}

.bg_image .exclusive__content__blk {
    position: absolute;
    bottom: 100px;
    width: 95%;
    padding: 0;
    left: 0;
    right: 0;
    color: #fff;
    text-align: left;
}

.bg_image .exclusive__content__blk p {
    color: #fff;
}

.exclusive__area .col-lg-4 {
    padding: 0;
}

.exclusive__area .container {
    max-width: 1920px;
}

.title_studio {
    font-size: 70px;
    font-family: 'picadilly', sans-serif;
    color: #eff1f2;
    align-self: baseline;
    margin: 0;
    padding-left: .5em;
    text-transform: uppercase;
    letter-spacing: 40px;
}

.point h3 {
    color: #5a6c76;
    font-weight: 900;
    font-size: 40px;
    font-family: 'picadilly', sans-serif;
}

.single-exclusive-thumb {
    height: 775px;
}

.single-exclusive-thumb img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center;
    filter: grayscale(1);
    transition: .3s all;
}

.owl-stage {
    padding-top: 5em;
}

.exclusive__inner__blk .owl-nav button {
    height: 70px;
    width: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30px !important;
    color: #000 !important;
    /*background: #fff !important;*/
    /*border-radius: 50%;*/
    /*box-shadow: 0 8px 20px 0 rgba(0, 0, 0, .1);*/
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 12%;
}

.exclusive__inner__blk .owl-nav button {
    height: 130px;
    width: 130px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30px !important;
    color: #000 !important;
    /*background: #d4e5fa !important;*/
    /*border-radius: 50%;*/
    /*box-shadow: 0 8px 20px 0 rgba(0, 0, 0, .1);*/
    position: absolute;
    top: 25%;
    transform: rotate(180deg);
    left: 11%;
    transition: .3s ease;
}


#home .exclusive__inner__blk .owl-nav button {
    top: 19%;
    left: 9%;
    transition: .3s ease;
}


button.owl-prev img:hover {
    /*transform: scale(.9);*/
    border: none;
    /*background: #d4e5fa !important;*/
    /*border-radius: 50%;*/
}

.exclusive__inner__blk .owl-nav button.owl-next {
    display: none;
}


.exclusive__inner__blk .owl-nav button.owl-prev {
    position: absolute;
    transform: rotate(0deg);
}

.exclusive__content__blk {
    max-width: 1450px;
    margin: 0 auto;
    padding: 0;
}

.exclusive__content h4 {
    font-size: 20px;
    color: #d4d9db;
    line-height: 35px;
    text-transform: uppercase;
    font-weight: 800;
    letter-spacing: 5px;
}

#single-template .exclusive__content h4 {
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 5px;
}

.exclusive__content h5 {
    font-size: 24px;
    color: #ABACB4;
    font-weight: normal;
}

.exclusive__content p {
    font-size: 38px;
    color: #212121;
    margin: 0;
}

#single-template .exclusive__content__blk p {
    font-size: 16px;
    padding-top: 0;
    padding-left: 2em;
}

.exclusive__content__blk p {
    font-size: 18px;
    padding-top: 0;
    color: #5a6c76;
}

#home .light-grey-text {
    width: 85%;
    margin: 0 auto;
}

#home .light-grey-text p {
    font-size: 16px;
    padding-top: 20px;
    color: #5a6c76;
}

#home .light-grey-text .large-letter {
    font-size: 40px;
    font-weight: bolder;
}

#home .light-grey-text .bold-grey-text {
    font-weight: bolder;
}

.owl-item.active.center .single-exclusive-thumb img {
    filter: brightness(1);
}

.exclusive__inner__blk .owl-item.active {
    filter: none
}

.exclusive__inner__blk .owl-item {
    transform: scale(1);
    transition: transform 0.5s ease;
}

/* ----------------------------------------- EXCLUSIVE AREA END ------------------------ */


/* ----------------------------------------- STUDIO AREA START ------------------------ */
.studio__thumb {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    position: absolute;
    height: 100%;
    width: 100%;
}

.studio__thumb.active {
    opacity: 1;
}


#expertise .studio__right__blk {
    position: sticky;
    top: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding: 20px;
}

.studio__right__blk {
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
}


.studio__thumb img {
    height: 100%;
    width: 100%;
    object-fit: contain;
    object-position: center bottom;
}

#home .studio__thumb img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center bottom;
}

.single__studio__contene h3 {
    font-size: 32px;
    font-weight: 600;
    color: #D4E5FA;
    margin-bottom: 10px;
    text-transform: uppercase;
    transition: .3s all;
}

#expertise .single__studio__contene h3 {
    font-size: 32px;
    font-weight: 900;
    color: #232323;
    margin-bottom: 35px;
    text-transform: capitalize;
    transition: .3s all;
}

#home .single__studio__contene h3 {
    font-size: 40px;
    font-weight: 900;
    color: #d4d9db;
    margin-bottom: 21px;
    text-transform: uppercase;
    transition: .3s all;
    font-family: 'picadilly', sans-serif;
    letter-spacing: 20px;
}

#home .single__studio__contene h3:hover {
    color: #6c757d;
}

#home .studio__area h3:hover ~ .studio-thumb-1 img {
    filter: grayscale(1);
}

.single__studio__contene p {
    color: #ABACB4;
    font-size: 14px;
    margin: 0;
}

.studio__left__blk {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    gap: 100px;
    max-width: 300px;
    padding: 80px 0;
}

#home .studio__left__blk {
    max-width: 66%;
}

#expertise .studio__area {
    height: 100vh;
    display: flex;
}

#expertise .studio__left__blk {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    /*height: 100%;*/
    gap: 70px;
    max-width: 100%;
    padding-left: 80px;
    padding-top: 0;
    padding-bottom: 0;
    flex: 1; /* Allows the left block to take the remaining space */
    overflow-y: auto; /* Enables vertical scrolling */
    scroll-behavior: smooth;
}

.single__studio__contene {
    cursor: pointer;
}

.studio__area {
    margin: 50px 0 150px 0;
    overflow: hidden;
    position: relative;
    /*height: calc(100vh - 70px);*/
}

#expertise .studio__area h2 {
    z-index: 10;
    position: relative;
}

.contact__logo {
    text-align: center;
    margin-bottom: 40px;
    gap: 30px;
    opacity: 0.5;
    @media (max-width: 768px) {
        margin-top:8em;
    }
}

img.approach_footer_crest {
    @media (max-width: 768px) {
        transform: scale(.7);
        padding: 0;
        margin: 0 !important;
    }
}

.contact__logo img {
    max-width: 204px;
}

.contact__logo span {
    font-size: 20px;
    color: #ABACB4;
    letter-spacing: 18px;
    font-weight: bold;
    font-family: 'picadilly', sans-serif;
    padding-bottom: 1em;
}

.contact__btn {
    text-align: center;
}

.contact__btn a {
    color: #ABACB4;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    transition: .3s all;
    padding: 25px 45px;
    display: inline-flex;
    align-items: center;
    letter-spacing: 3px;
    border: 1px solid white;
}

.contact__btn__nav {
    color: #ABACB4;
    transition: .3s all;
    padding: 0 !important;
    margin: 0 20px;
    align-items: center;
    letter-spacing: 3px;
    border-top: 1px solid white;
    border-bottom: 1px solid white;
    @media (max-width: 1200px) {
        padding: 0 !important;
        margin: 0 20px;

    }
}

.contact__btn a:hover {
    color: white;
    border: 1px solid #ABACB4;
    background-color: #ABACB4;
}

.contact__btn__nav:hover {
    color: #3172BF;
    border-top: 1px solid #ABACB4;
    border-bottom: 1px solid #ABACB4;
}

.contact__area {
    padding-bottom: 100px;
}

.adaptability__text p {
    font-size: 16px !important;
}

#single-template .principle_section .text {
    padding-left: 60px !important;
    @media (max-width: 1024px) {
        padding-left: 0 !important;
    }
}

.award__text p {
    font-size: 16px !important;
    margin-right: 80px;
    min-width: 300px;
    @media (max-width: 999px) {
        font-size: 14px;
        margin-right: 0;
    }
}

.crafted__design__text {
    margin-left: 160px;
    margin-right: 100px;
    max-width: 450px;
    min-width: 370px;
    @media (max-width: 999px) {
        min-width: 100px;

    }
}

.extraordinary__header, .crafted__design__header, #founders h4, .approach__section h4 {
    font-size: 16px !important;
    text-transform: uppercase;
}

.links__text a {
    display: flex;
    flex-direction: column;
    font-size: 18px !important;
    margin: 40px 0;
    text-decoration: none;
    color: #232323;
}

.article__links__text a {
    display: flex;
    flex-direction: column;
    font-size: 14px !important;
    margin: 30px 0;
    text-decoration: none;
    color: #232323;
}

.links__text a:hover, .article__links__text a:hover {
    color: #D4E5FA;
}

.award__section {
    margin: 30px 0;
}

.award__section h4 {
    font-size: 16px !important;
}

.boat__nominee__image {
    width: 200px !important;
    height: auto
}

.links__title {
    margin: 100px 0;
}

.article__links__title {
    margin: 50px 0;
}

/* ----------------------------------------- STUDIO AREA END ------------------------ */

/* ----------------------------------------- IMAGE GRID START ------------------------ */
.image__grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(2, auto);
    gap: 6px;
}

/* ----------------------------------------- IMAGE GRID END ------------------------ */


/* ----------------------------------------- FOOTER AREA START ------------------------ */
.footer__area {
    padding: 80px 0 0;
    background: #f3f3f2;
    justify-content: center;
    margin: auto;
    text-align: center;
    align-items: center;
}

.footer__area .container {
    max-width: 1600px;
}

.footer__mail {
    text-align: center;
    margin: auto;
    justify-content: center;
    align-items: center;
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
}

.footer__mail a {
    color: #cfcfd0;
    font-size: 16px;
    display: flex;
    transition: .3s all;
    padding-top: 1em;
}

.footer__privacy a {
    font-size: 20px;
    color: #cfcfd0;
    display: block;
    transition: .3s all;
    margin-bottom: 6px;
}

.footer__privacy a:hover {
    color: #9CC9FF;
}

.footer__social__ico a {
    font-size: 25px;
    height: 55px;
    width: 55px;
    display: flex;
    justify-content: center;
    align-items: center;
    /*background: #fff;*/
    border-radius: 10px;
    color: #cfcfd0;
    transition: .3s all;
}

span.__cf_email__ {
    font-size: 18px;
}

.footer__social__ico {
    display: flex;
    gap: 20px;
    margin: auto;
    justify-content: center;
}

.footer__social__ico a:hover {
    color: #ABACB4;
    border: 1px solid #ABACB4;
    /*background: #cfcfd0;*/
}

.footer_bottom {
    border-top: 2px solid #fff;
}

.footer_bottom p {
    padding-top: 18px;
    color: #cfcfd0;
    font-size: 16px;
    letter-spacing: 3px;
    text-align: center;
}

.footer__content a {
    color: #cfcfd0;
    font-size: 16px;
    transition: .3s all;
    display: block;
    margin-bottom: 5px;
}

.footer__copyright p {
    color: #cfcfd0;
    font-size: 16px;
    text-align: center;
}

.footer__mail a:hover {
    color: #223852;
}

.footer__content a:hover {
    color: #223852;
}

.footer__identity img {
    height: 100px;
}

/* ----------------------------------------- FOOTER AREA END ------------------------ */


/* ----------------------------------------- MOBILE MENU AREA START ------------------------ */

.mobile__menu .mega__menu {
    position: unset;
    width: 100%;
    flex-direction: column;
    top: 0 !important;
    visibility: visible;
    opacity: 1;
    gap: 20px;
    padding: 15px;
}

.mobile__menu .header__right__blk {
    flex-direction: column;
    align-items: flex-start;
}

.mobile__menu .header__right__blk .main__menu ul li {
    margin: 0;
    display: block;
    padding: 6px 0;
}

.mobile__menu .header__right__blk .main__menu ul li a {
    display: inline-flex;
    padding: 6px 0;
    border-bottom: 1px solid #000;
    width: 100%;
    border-radius: 0 !important;
    background: #fff !important;
    box-shadow: none !important;
}

.mobile__menu .header__right__blk .main__menu {
    width: 100%;
}

.mobile__menu .header__btn {
    margin: 20px 0;
    width: 100%;
}

.mobile__menu .social__ico__blk {
    margin-left: 0;
    justify-content: center;
    width: 100%;
}

.mobile__menu {
    display: block;
    position: fixed;
    top: 0;
    height: 100%;
    z-index: 999;
    background: #fff;
    width: 330px;
    padding: 15px;
    padding-top: 120px;
    transition: .3s all;
    left: -100%;
}

.mobile__menu .header__btn a {
    width: 100%;
}

.close__menu {
    position: absolute;
    top: 20px;
    right: 20px;
    color: #000;
    font-size: 20px;
    line-height: 1;
    cursor: pointer;
}

.mobile__menu .header__right__blk .main__menu .main__menu__link a {
    border: 0;
    padding: 0;
    font-size: 14px;
    margin: 0;
}

.mobile__menu .mega_menu_right p {
    font-size: 15px;
}

.none__desk {
    display: none;
}

.open__menu {
    font-size: 22px;
    cursor: pointer;
    line-height: 1;
}

.overlay {
    position: fixed;
    left: -100%;
    top: 0;
    height: 100%;
    width: 100%;
    background: #000;
    z-index: 555;
    transition: .5s all;
    opacity: 0.7;
}

/* ----------------------------------------- MOBILE MENU AREA END ------------------------ */

/* ----------------------------------------- BOX AREA START ------------------------ */

.box_area {
    max-width: 1500px;
    padding-top: 0;
    margin-top: 0;
}

#founders {
    background-image: url("../images/sturge-toth-crest-beige.svg");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    background-color: white;
    /*background-color: #f7f5f3;*/
}

.bio_box_area {
    /*background-color: white;*/
    padding-top: 10px;
}

.box {
    background: white;
    margin-bottom: 4em;
}

.box h3 {
    text-align: left;
    padding: 15px 0;
    color: #5a6c76;
    font-size: 25px;
    font-weight: 900;
    text-transform: uppercase;
}

.box p {
    color: #5a6c76;
    font-size: 16px;
}

.box strong {
    font-size: 18px;
    color: #5a6c76;
    font-weight: 900;
}

img#main-image {
    @media (min-width: 1000px) {
        padding-left: 5em;
    }
}

.bg_image h4, p {
    color: #ffffff;
}

article p {
    color: #5a6c76;
}

#founders .box {
    max-width: 80%;
    margin: 0 40px;
}

/* ----------------------------------------- BOX AREA END ------------------------ */
.expertise__section {
    margin: 20px;
}

.expertise__section h2 {
    padding-left: 60px;
}

.expertise__container {
    margin: 30px 0;
    display: flex;
    flex-direction: row;
    gap: 40px;
    align-items: start;
    justify-content: center;
    width: 100%;
    @media (max-width: 1024px) {
        flex-direction: column;
    }
}

.expertise__card {
    max-width: 450px;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 30px;
    width: 30%;
    justify-content: space-between;
    @media (max-width: 1024px) {
        width: auto;
    }
}

.expertise__card img {
    object-fit: cover;
    width: 450px;
    height: 300px;
    overflow: hidden;
    @media (max-width: 1024px) {
        width: auto;
        max-height: 250px;
    }
}

/* ----------------------------------------- ANIMATIONS START ------------------------ */
.commitment__content h4 {
    opacity: 1; /* Ensure it's visible initially */
    color: black; /* Initial color */
    transition: color 0.3s ease; /* Smooth transition for color */
}

.line {
    position: relative;
}

.line-mask {
    position: absolute;
    top: 0;
    right: 0;
    background-color: black;
    opacity: 0.65;
    height: 100%;
    width: 100%;
    z-index: 2;
}

.header__burger .line-mask {
    position: absolute;
    top: 0;
    right: 0;
    background-color: white;
    opacity: 0.65;
    height: 100%;
    width: 100%;
    z-index: 2;
}

.exclusive__inner__blk {
    position: relative; /* Ensure positioning for absolute arrows */
}

.exclusive__inner__blk:hover .owl-nav {
    display: block; /* Show arrows on hover */
}

/* Define the custom cursor for the arrows */
.exclusive__inner__blk {
    cursor: url('../images/cursor.png'), auto; /* Default cursor */
}

/* Left half of carousel */
#home .exclusive__inner__blk:hover {
    cursor: w-resize; /* West-pointing arrow for left side */
}

/* Right half of carousel */
#home .exclusive__inner__blk:hover {
    cursor: e-resize; /* East-pointing arrow for right side */
}

/* For the navigation buttons specifically */
#home .owl-prev {
    cursor: w-resize;
    width: 60px;
    height: auto;
}

#home .owl-next {
    cursor: e-resize;
    width: 60px;
    height: auto;
}

/* Add some styling for the navigation arrows if needed */
.exclusive__inner__blk .owl-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);

}

#home .exclusive__inner__blk .owl-nav button.owl-next {
    display: block;
    left: 87%;
    position: absolute;
}

.header__btn.desktop {
    margin-top: 3em;
}

.exclusive__inner__blk .owl-nav .owl-prev,
.owl-nav .owl-next {
    opacity: 0.8; /* Slightly transparent for aesthetic */
    transition: opacity 0.3s;
    display: block;
    z-index: 15;
}

.exclusive__inner__blk .owl-nav .owl-prev:hover,
.owl-nav .owl-next:hover {
    opacity: 1; /* Fully visible on hover */
}

.exclusive__inner__blk .owl-nav .owl-prev img:hover {
    opacity: 1; /* Fully visible on hover */
}

#expertise .header__logo {
    transition: .4s all;
    transform: scale(1);
}

#single-template .header__logo {
    transition: .4s all;
    transform: scale(1);
}


/* Container styling */
.scroll-container {
    width: 100%;
    overflow: hidden;
}

/* Each section */
.scroll_sticky {
    position: relative;
    padding: 80px 0;
}

/* Wrapper styling for pinning */
.scroll_sticky .wrapper {
    position: relative;
    width: 100%; /* Full width from your style */
    margin: 0 auto;
    height: auto; /* Dynamfheroic height from your style */
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 2em !important;
}

/* Points (slides) container */
.scroll_sticky .points {
    position: relative;
    width: 100%;
    height: 70vh;
    overflow: visible;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
}

/* Individual slide styling */
.scroll_sticky .point {
    position: absolute;
    height: 60vh;
    width: 100%;
    padding: 4rem;
    padding-top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    text-align: left;
    transition: opacity 0.5s, transform 0.5s;
    opacity: 1;
    /*transform: translateY(50px); !* Slide-in effect *!*/
    padding-left: 0;
    padding-bottom: 0;
}

.point img {
    flex-basis: 60%;
    max-width: 60%;
    height: 760px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(60px);
}

.point article {
    flex-basis: 40%; /* Content width from your style */
    margin-right: auto;
    padding-left: 3em; /* Additional left padding from your style */
    font-size: 1.2rem;
    color: #333;
    line-height: 1.5;
    padding-top: 4em;
}

.point p {
    color: #ABACB4;
    font-size: 16px;
}

/* Indicators styling */
.indicators {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.indicator {
    opacity: 0;
}

.point img {
    border-radius: 0;
}

/* Active state for animations */
.scroll_sticky .point.active {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .scroll_sticky .wrapper {
        width: 90%;
    }

    .point article {
        font-size: 1rem;
    }
}

.principle_section, .ethics_section {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin: auto;
    padding: 10em 0;

    @media (max-width: 768px) {
        flex-direction: column;
        padding: 7em 2em;
        max-width: 100vw;
    }
}

.ethics_section {
    padding: 0;
}

.ethics_section img {
    padding: 0 2em;
}

.ethics_text {
    padding-right: 40px;
    @media (max-width: 999px) {
        padding-right: 0;
    }
}

.image-container {
    position: relative;
    width: 100%;
}

.principle_section img, .ethics_section img {
    max-width: 100%;
    height: auto;
}

.principle_section .image__grid img, .ethics_section .image__grid img {
    min-width: 160px;
    width: 180px !important;
    height: 180px !important;
    object-fit: cover;
    @media (max-width: 999px) {
        width: 160px;
        height: 160px !important;
    }
}

/*.clickable-area {*/
/*    position: absolute;*/
/*    cursor: pointer;*/
/*}*/

.home_intro_section {
    width: 45%;
    margin: 100px auto;
    padding: 0 1em;
}

.home_intro_section p {
    color: #5a6c76;
    font-size: 16px;
}

.home_intro_section strong {
    font-size: 20px;
    font-weight: 900;
    letter-spacing: 3px;
    color: #223852;
}

.home_intro_section h2 {
    color: #d4d9db;
    text-transform: uppercase;
    letter-spacing: 8px;
    font-size: 48px;
    padding-bottom: 1em;
}

.home_intro_section h2 .closer-hero-text {
    letter-spacing: 4px;
}

.principle_section p, .ethics_section p,  .founders_section p, .approach__section p {
    color: #5a6c76;
    font-size: 14px;
    padding-top: 1em;
    margin-bottom: 0;
}

.approach__section p.italic {
    font-style: italic;
}

.principle_section strong, .ethics_section strong {
    font-size: 30px;
    font-weight: 900;
    letter-spacing: 3px;
    color: #223852;
}

.principle_section h2, .ethics_section h2 {
    color: #d4d9db;
    text-transform: uppercase;
    letter-spacing: 3px;
    font-size: 25px;
    padding-bottom: 1em;
    /*margin: 0 1em;*/
    @media (max-width: 999px) {
        font-size:20px;
    }
}

 .founders_section h2 {
    color: #d4d9db;
    text-transform: uppercase;
    letter-spacing: 3px;
    font-size: 25px;
    padding-bottom: 1em;
    margin: 0 0.5em;
}

.studio_hero_section h2 {
    color: #d4d9db;
    text-transform: uppercase;
    letter-spacing: 3px;
    font-size: 35px;
    padding: 1em 0.5em;
    line-height: 50px
}

.studio_hero_section {
    /*padding: 100px 0 0;*/
    margin: auto;
    width: 50%;
    @media (max-width: 1024px) {
        width: 100%;
    }
}


@media (max-width: 768px) {
    .principle_section .text h2 {
        color: #223852;
        font-size: 40px;
        padding-bottom: 10px;
        padding-top: 1em;
    }

    .principle_section .text p, .ethics_section .text p {
        font-size: 16px;
    }

    .home_intro_section {
        width: 100%;
        margin: 40px auto;
    }
}

.principle_section .text p {
    color: #697982;
    font-size: 18px;
}

section.exclusive__area.second {
    padding-bottom: 3em;
}

#expertise section.contact__area {
    padding-top: 5em;
}

.line-clamp-4 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    /* truncate to 4 lines */
    -webkit-line-clamp: 4;
}

.fade-scale {
    transition: opacity 0.8s ease, transform 1s ease;
    opacity: 1;
    transform: scale(1);
}

/* Temporary fade-out and scale-down */
.fade-out {
    opacity: 0;
    transform: scale(0.98);
}

footer.footer__area {
    background: url(../images/sturge-toth-crest-white.svg) no-repeat, url(../images/sturge-toth-crest-white.svg) no-repeat;
    background-color: #f3f3f2;
    background-position: -15% center, 115% center;
    background-size: 25%;
    left: 0;
    right: 0;
    position: absolute;
}

#home .header__btn a:hover, .header__btn a:hover {
    color: #6c757d !important;
}


.exclusive__content__blk.second {
    text-align: left !important;
    padding-left: 5em;
}

#home .accordion-content-jc p {
    text-align: center;
    @media (min-width: 1000px) {
        font-size: 20px;
        line-height: 1.5;
        width: 100%;
    }
}

.accordion-header-jc-unique.mb {
    @media (min-width: 1000px) {
        left: -5%;
        position: relative;
    }
}

#home header.header__area.sticky {
    display: block;
}

#home header.header__area {
    display: none;
}

/* Basic styles for the sticky header */
body#home .header__area {
    position: sticky;
    top: 0;
    width: 100%;
    background-color: #fff; /* Adjust this as needed */
    z-index: 10;
    opacity: 0; /* Initially hidden */
    transform: translateY(-100%); /* Initially off-screen */
    transition: transform 0.5s ease-out, opacity 0.5s ease-out; /* Smooth slide and fade */
}

/* When the sticky header becomes visible */
body#home .header__area.sticky.visible {
    opacity: 1; /* Fully visible */
    transform: translateY(0); /* Slide into view */
}


#single-template .principle_section {
    padding-bottom: 5em;
}

#single-template .principle_section.pb {
    padding-bottom: 10em;
}

section.exclusive__area.second {
    padding: 10em 5em;
}


#menu-open-logo {
    transform: scale(0.8); /* Example styling for menu-open logo */
    transition: transform 0.3s ease;
}

.bg-gray {
    background: #f3f3f1;
    height: 570px;
    display: block;
    padding: 50px 0;
}

.text_media {
    padding-top:5em;
    .commitment__content {
        text-align: center;
        position: relative; /* Ensures it sits centered */
        left:0;
        align-items: normal;
    }

    .commitment__content h4 {
        font-size: 60px;
        font-weight: unset;
        margin-bottom: 20px;
        line-height: 1.7em;
        text-align: left;
        font-family: 'picadilly';
        max-width: 100%;
        padding-left: 1.5em;
        padding-top:20px;
        @media (max-width: 767px) {
            padding-left: 0;
            font-size: 45px;
        }
        @media only screen and (min-width: 768px) and (max-width: 991px) {
            padding-left: 0;
            width: 70%;
        }
    }

    .commitment__content img {
        max-width: 30%;
        height: auto;
        display: block;
        margin: 0 auto;
        margin-top: -60px;
        margin-left: 55%;
        @media (max-width: 767px) {
            max-width: 50%;
            height: auto;
            display: block;
            margin: 0 auto;
            margin-top: 0;
            margin-left: 40%;
        }
    }
    .line-mask {
        background-color: #f3f3f1;
    }
    .line {
        text-align: left !important;
    }
}

h4 {
    color: #5a6c76;
}

@media only screen and (min-width: 768px) {
    section.content_image img {
        width: 100%;
        margin: auto;
        height: 700px;
        object-fit: cover;
    }
}

#home .accordion-header-jc-unique {
    text-align: center ;
}

.scroll-arrow {
    text-align: center;
    margin-top: 20px;
    position: absolute;
    bottom: 20px;
    left: 0;
    right:0;
    transform: rotate(270deg);
    z-index: 10;
}

.scroll-arrow a img {
    width: 30px;
    height: auto;
    animation: bounce 1.5s infinite;
    cursor: pointer;
    opacity: 0.8;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* ----------------------------------------- ANIMATIONS END ------------------------ */