/* ----------------------------------------- HORIZONTAL SCROLL AREA START ------------------------ */
.horizontal-scroll-container {
    display: flex;
    flex-direction: row;
    height: 100vh;
    width: 100%;
    position: relative;
    cursor: move;
    @media (max-width: 999px) {
        scroll-snap-type: x mandatory;
    }
}

.horizontal-scroll-container section {
    margin-top: 100px;
    height: calc(100vh - 100px);
}

.horizontal-scroll-container .container {
    max-width: 100%;
    /*overflow-x: hidden;
    */
    /*overflow-y: auto;
    */
    /*padding: 2em 0;
    */
    padding-bottom: 70px;
}

.middle-line {
    position: absolute;
    top: 100%;
    left: 50%;
    width: 97vh;
    height: 2px;
    background-color: white;
    z-index: 10;
    transform: translateX(-81%) rotate(90deg);
}

.header__logo_visible {
    width: 25%;
    transition: .3s ease;
}

.header__logo_visible:hover {
    transition: .3s ease;
    transform: scale(0.95);
}

/*.horizontal-scroll-container {
	*/
/* display: flex;
*/
/* flex-direction: row;
*/
/* overflow: scroll;
*/
/* height: 100vw;
*/
/* scroll-behavior: smooth;
*/
/* !*transform: rotate(-90deg) translateX(-65%);
*!*/
/* overflow-x: scroll;
*/
/* !*transform-origin: left top;
*!*/
/* @media (max-width: 768px) {
    */
/* !*transform: rotate(-90deg) translateX(-90%);
*!*/
/*
}
*/
/*
}
*/
/*.horizontal-scroll-container .section {
	*/
/* max-height: 100vh;
*/
/*
}
*/
.horizontal-scroll-container .section .content-wrapper {
    margin: 0 auto;
}

.section {
    /*transform: rotate(90deg) translateY(-100%);
    */
    /*transform-origin: left top;
    */
    width: 100vw;
    height: 120vh;
    flex-shrink: 0;
    background: #dceaf2;
    display: flex;
    justify-content: center;
    align-items: center;
    @media (max-width: 768px) {
        padding: 20px;
        /*height: 100% !important;
        */
        height: 85vh;
    }
}

.section:not(:last-child) {
    margin-bottom: 0;
}

.section-2, .section-3 {
    color: #333;
}

.content-wrapper {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    width: 100vw;
    height: 100vh;
    text-align: center;
    /*max-width: 1600px;
    */
    @media (max-width: 1024px) {
        flex-direction: column;
        align-items: center;
        width: 100%;
    }
    @media (max-width: 768px) {
        flex-direction: column;
        padding: 20px !important;
        width: 100%;
    }
}

.section-1 {
    background-color: #dceaf2;
    padding: 0;
    margin: 0;
}

.section-1 .content-wrapper {
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: relative;
    /*padding: 0;*/
    height: 100%;
    @media (max-width: 768px) {
        height: auto;
        padding: 10px;
        flex-direction: column;
        align-items: center;
    }
}

.text-content {
    width: 45%;
    position: absolute;
    top: 12em;
    left: 3em;
    text-align: left;
    color: #fff;
    @media (max-width: 1080px) {
        width: 60%;
        position: absolute;
    }
    @media (max-width: 999px) {
        width: 100%;
        /*position: relative;*/
        top: 0;
        @media (max-width: 998px) {
            width: 70%;
            /*position: relative;*/
            top:-1em;
            left: auto;
            /*padding: 30px;*/
        }
        @media (max-width: 768px) {
            width: 80%;
            top: 1em;
        }
    }
}

.text-content blockquote {
    font-size: 1.2em;
    color: #6d7d86;
    margin: 0 0 1em 0;
    line-height: 1.5em;
    position: relative;
    padding-left: 5em;
    font-family: 'poppins', sans-serif;
    @media (max-width: 768px) {
        padding-left: 0;
        font-size: 1em;
    }
}

.text-content blockquote:before {
    content: url('../images/quote.png');
    position: absolute;
    left: 0;
    top: -2em;
    width: 1.5em;
    height: auto;
    @media (max-width: 768px) {
        top: -8em
    }
}

.text-content p {
    margin: 1em 0;
    font-size: 14px;
    color: #6d7d86;
    font-weight: 900;
    @media (max-width: 768px) {
        font-size: 12px;
        margin: 0.5em 0;
    }
}

.text-content p:last-child {
    margin-top: 0;
    margin-bottom: 0;
}

.title {
    font-size: 90px;
    font-weight: 900;
    position: absolute;
    top: .7em;
    left: 1.5em;
    text-transform: uppercase;
    letter-spacing: 70px;
    font-family: 'picadilly', sans-serif;
    color: #eff3f7;
    @media (min-width: 769px) and (max-width: 1200px) {
        letter-spacing: 60px;
        font-size: 65px;
    }
    @media (max-width: 768px) {
        font-size: 35px;
        position: relative;
        top: -5em;
        /*top: -11em;*/
        left: 0;
        text-align: left;
        letter-spacing: 20px;
    }
}

.content-wrapper {
    display: flex;
    justify-content: flex-start;
    width: 100vw;
    height: 100%;
    position: relative;
    padding: 0;
/ @media (max-width: 768 px) {
    width: 100%;
    height: auto;
} /*max-width: 1600px;
    */
}

.section {
    /*height: auto;
    */
    overflow: visible;
}

.content-wrapper {
    /*height: auto;*/
    overflow: visible;
}

.briefing {
    overflow: visible;
}

.images {
    width: 50%;
}

.images img {
    width: 100%;
}

.briefing {
    width: 40%;
    background-color: white;
    padding: 20px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

h3 {
    font-size: 28px;
    color: #0d3252;
}

p {
    font-size: 18px;
    color: #333;
}

.section-2 .briefing p {
    margin-top: 10px;
    color: #444;
}

#horizontal_scroll .header__area {
    width: 100%;
    /*background-color: #ddeaf2;*/
    /*padding: 20px 0;*/
}

#horizontal_scroll .header__inner__blk {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 100%;
    margin: 0 auto;
    /*padding: 0 50px;*/
}

#horizontal_scroll .header__logo_visible a img {
    max-height: 50px;
}

/*#horizontal_scroll .header__btn a {
    */
/* font-size: 16px;
*/
/* color: #333;
*/
/* text-decoration: none;
*/
/* padding: 10px 20px;
*/
/* background-color: #f4f4f4;
*/
/* border-radius: 5px;
*/
/* letter-spacing: 3px;
*/
/*
}
*/

.section-2, .section-3 .content-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2
}

.images-wrapper {
    position: relative;
    width: 70%;
    display: flex;
    justify-content: center;
    align-items: center;
    @media (max-width: 1024px) {
        width: 100%;
        flex-direction: column;
    }
    /* Mobile adjustments */
    @media (max-width: 768px) {
        width: 100%;
        flex-direction: column;
    }
}

.section-3 .images-wrapper {
    position: relative;
    width: 100%;
    display: flex;
}

.images-wrapper.first {
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    @media (max-width: 768px) {
        justify-content: center;
    }
}

.thumbnail {
    transform: rotate(180deg);
    width: 10%;
    @media (max-width: 768px) {
        width: 40%;
    }
}

.image-1 {
    width: 70%;
    float: left;
    margin-right: 0;
    position: relative;
    z-index: 2;
    top: 100px;
    right: 100px;
}

.image-2 {
    width: 70%;
    float: right;
    position: relative;
    z-index: 1;
}

img.image-3 {
    z-index: 10;
    position: absolute;
    top: 90px;
    width: 35%;
}

.image-4 {
    width: auto;
    position: absolute;
    bottom: -180px;
}

.image-1, .image-2, .image-3, .image-4 {
    @media (max-width: 768px) {
        width: 100%;
    }
}

.section-2 .content-wrapper, .section-3 .content-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    @media (max-width: 768px) {
        /*flex-direction: column;*/
    }
}

.center-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #fff;
    transform: rotate(90deg);
    z-index: 0;
    height: 100%;
}

/* Briefing section (Text block) */

.briefing {
    width: 40%;
    background-color: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    text-align: left;
    padding: 1em;
    @media (max-width: 768px) {
        width: 100%;
        padding: 1em;
    }
}

.briefing h3 {
    font-size: 40px;
    color: #0d3252;
    margin-bottom: 15px;
    font-family: 'picadilly', sans-serif;
    line-height: 28px;
    @media (max-width: 768px) {
        font-size: 24px;
    }
}

.briefing strong {
    font-size: 26px;
    color: #232323;
    margin-top: 10px;
    line-height: 32px;
    font-family: 'poppins', sans-serif;
    font-weight: 500;
    @media (max-width: 768px) {
        font-size: 18px;
    }
}

.briefing p {
    font-size: 18px;
    color: #333;
    margin-top: 10px;
    line-height: 1.5em;
}

#expertise section.contact__area {
    padding-top: 3em;
}

#single-template section.contact__area {
    padding: 3em 0 8em;
}

@media (max-width: 768px) {
    .header__logo_visible img {
        max-height: 30px;
    }

    .header__btn a {
        font-size: 14px;
        padding: 8px 16px;
    }
}

#nextArrow {
    cursor: pointer;
    width: 135px;
    position: absolute;
    right: 150px;
    bottom: 0;
    z-index: 1000;
    top: 49%;
}

#nextArrow {
    @media (max-width: 768px) {
        cursor: pointer;
        width: 80px;
        position: absolute;
        right: 50px;
        bottom: 0;
        z-index: 1000;
        top: 30%;
    }
}

#PrevArrow {
    cursor: pointer;
    width: 135px;
    position: absolute;
    left: 50px;
    bottom: 0;
    z-index: 1000;
    top: 50%;
}

@media (min-width: 1000px) {
    #PrevArrow {
        display: none;
    }
}

.l-center {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    /*padding-bottom: 70px;*/
    /*padding-top: 80px;
    */
}

.l-grid {
    display: grid;
    grid-template-columns:minmax(400px, 50%) 1fr;
    grid-gap: 0;
    align-items: stretch;
}

@media (min-width: 1800px) {
    .section-1 .l-grid {
        display: grid;
        grid-template-columns:minmax(400px, 50%) 1fr;
        grid-gap: 0;
        align-items: stretch;
    }

    .section-1 .o-aspect-ratio[data-padding="66"]::before {
        padding-top: 66%;
    }

    .section-1 .box_area {
        width: 60% !important;
    }

    .section-1 .o-overlap-bottom, .o-overlap-top {
        position: absolute;
        width: 75%;
    }

    .section-1 .o-overlap-bottom {
        left: 0;
        top: 0;
        z-index: 10;
    }
}

@media (max-width: 768px) {
    .l-grid {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        flex-direction: row;
    }
}

@media (max-width: 768px) {
    .l-grid {
        grid-template-columns: 100%;
        grid-gap: 40px;
    }
}

.l-max {
    max-width: 100%;
}

@media (max-width: 700px) {
    .l-max {
        max-width: 500px;
        margin: auto;
    }
}

#horizontal_scroll .box_area {
    width: 70%;
    margin-top: 0;
    background: #fff;
    padding: 3em;
    text-align: left;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 9;
    @media (min-width: 1900px) {
        width: 60%;
        height: 100%;
    }
    @media (max-width: 767px) {
        width: 100%;
        padding: 3em 30px;
    }
}

@media (min-width: 768px) {
    .section-2 .box_area {
        /*width: 85% !important;*/
    }
}

#horizontal_scroll {
    /*background: #ddeaf2;*/
}

#horizontal_scroll .box_area h3 {
    font-size: 25px;
    color: #c6cbcd;
    margin-bottom: 15px;
    font-family: 'picadilly', sans-serif;
    line-height: 1.1;
    text-transform: uppercase;
    letter-spacing: 5px;
    @media (max-width: 768px) {
        font-size: 18px;
    }
}


#horizontal_scroll .box_area strong {
    font-size: 25px;
    color: #6d7d86;
    margin-top: 10px;
    line-height: 40px;
    font-family: 'poppins', sans-serif;
    font-weight: 900;
    @media (max-width: 768px) {
        font-size: 18px;
    }
}

#horizontal_scroll .box_area p {
    font-size: 20px;
    color: #ABACB4;
    margin-top: 10px;
    line-height: 1.5em;
}

/* Object: Aspect Ratio */

.o-aspect-ratio {
    position: relative;
    overflow: hidden;
    @media (min-width: 768px) {
        min-height: 530px;
        height: 100%;
    }
}

.o-aspect-ratio::before {
    content: '';
    display: block;
    padding-top: 100%;
}

.o-aspect-ratio[data-padding="80"]::before {
    padding-top: 80%;
}

.o-aspect-ratio[data-padding="66"]::before {
    padding-top: 66%;
}

.o-aspect-ratio__media {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Object: Overlap */

.o-overlap-bottom, .o-overlap-top {
    position: absolute;
    width: 70%;
}

.o-overlap-bottom {
    left: 0;
    top: 0;
}

.o-overlap-bottom__deco {
    width: 15%;
    top: 0;
    right: 0;
    height: 100%;
    position: absolute;
    transform: translateX(95%);
}

.o-overlap-bottom__deco::before {
    content: '';
    position: absolute;
    width: 33.33%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.2;
    transform: translateX(100%);
}

.o-overlap-bottom__deco::after {
    content: '';
    position: absolute;
    width: 33.33%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.5;
    transform: translateX(200%);
}

.o-overlap-top {
    right: 0;
    bottom: 0;
}

.o-overlap-top__deco {
    position: absolute;
    top: 0;
    left: 0;
    width: 20%;
    opacity: 0.8;
    transform: translate(-130%, 115%);
}

#horizontal_scroll .header__logo {
    transition: .4s all;
    transform: scale(1);
}

.section-2 .o-overlap-bottom {
    width: 85%;
}

.section-2 .o-overlap-bottom .o-aspect-ratio__media {
    object-fit: cover;
}

.section-2 .box_area {
    width: 85%;
}

.section-2 .l-grid {
    display: grid;
    grid-template-columns: minmax(400px, 50%) 1fr;
    grid-gap: 0;
    /*align-items: center;*/
}

@media (max-width: 768px) {
    .section-2 .l-grid {
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
    }
}

.section-2 .o-overlap-top {
    position: absolute;
    width: 45%;
    left: 10%;
    padding-bottom: 5em;
}

.section-3 .o-overlap-bottom {
    width: 100%;
    left: 15%;
}

@media (max-width: 1799px) {
    .section-3 .o-aspect-ratio {
        /*overflow: inherit;*/
    }

    .section-3 .l-grid {
        display: grid;
        grid-template-columns:minmax(400px, 50%) 1fr;
        grid-gap: 0;
        align-items: stretch;
    }
}

@media (max-width: 768px) {
    .section-2 .l-grid {
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
    }
}

@media (min-width: 1800px) {
    .section-3 .l-grid {
        padding-bottom: 80px;
        align-items: end !important;
    }

    .section-3 .o-aspect-ratio {
        /*overflow: inherit;*/
    }

    .section-3 .o-overlap-bottom {
        width: 85% !important;
        left: 35% !important;
        top: 10% !important;
    }

    .section-3 .o-overlap-top {
        width: 46% !important;
        left: 20% !important;
    }
}

.section-3 .box_area {
    z-index: 10;
}

@media (min-width: 1700px) {
    .box_area {
        width: 100%;
    }
}

.section-3 .o-aspect-ratio__media {
    object-fit: cover;
}

.section-3 .o-overlap-top {
    width: 60%;
    left: 5%;
}

@media (max-width: 768px) {
    .horizontal-scroll-container {
        display: flex;
        flex-direction: row;
        /* Stack sections vertically */
        height: 100%;
        /* Full viewport height */
    }

    .section {
        width: 100%;
        /* Full width for each section */
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .o-aspect-ratio__media {
        position: relative;
    }

    .o-overlap-bottom__deco {
        display: none;
    }

    .o-aspect-ratio[data-padding="80"]::before {
        padding-top: 0;
    }

    .o-aspect-ratio[data-padding="66"]::before {
        padding-top: 10px;
    }

    .section-8 .o-aspect-ratio {
        position: relative;
        overflow: visible;
    }

    o-overlap-bottom, .o-overlap-top {
        position: relative;
        width: 100%;
    }

    .section-2 .o-overlap-top {
        position: relative;
    }

    .section-2 .o-overlap-bottom {
        position: relative;
    }

    #PrevArrow {
        cursor: pointer;
        width: 80px;
        position: absolute;
        left: 50px;
        bottom: 0;
        z-index: 1000;
        top: 30%;
    }

    .section-2 #PrevArrow {
        top: 46%;
    }

    .section-2 #nextArrow {
        top: 46%;
    }

    .section-2 .o-overlap-bottom {
        width: 100%;
    }

    .section-1 #nextArrow {
        top: 40%;
    }

    .section-1 #PrevArrow {
        top: 40%;
    }

    .section-3 #nextArrow {
        top: 22%;
    }

    .section-3 #PrevArrow {
        top: 22%;
    }

    #horizontal_scroll .box_area p {
        font-size: 12px;
        color: #ABACB4;
        margin-top: 5px;
        line-height: 1.5em;
    }

    #horizontal_scroll .box_area strong {
        line-height: 24px;
    }

    .section-3 .l-grid {
        grid-template-columns: 100%;
    }

    .section-3 .o-overlap-bottom {
        width: 100%;
        left: 0;
    }

    .section-3 .o-overlap-top {
        width: 100%;
        left: 0;
    }
}

.horizontal-line {
    position: absolute;
    top: 71%;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: white;
    /*background-image: url("images/sketch-line.png");*/
    z-index: 1;
}

.split {
    position: absolute;
    top: 76.8%;
    right: 0;
    width: 40%;
    height: 2px;
    background-color: white;
    z-index: 1;
    left: auto;
}

.section {
    position: relative;
    overflow: hidden;
    /* Ensures content stays within the section */
}

.section-4 .o-overlap-bottom {
    left: auto;
    top: 0;
    right: 0;
    z-index: 10;
}

.section-4 .o-overlap-top {
    right: auto;
    left: 0;
}

.section-4 .l-grid {
    display: grid;
    grid-template-columns:minmax(400px, 50%) 1fr;
    grid-gap: 0;
    align-items: stretch;
}

.section-5 .l-grid {
    align-items: stretch;
    grid-template-columns:minmax(400px, 50%) 1fr;
}

.section-5 .o-aspect-ratio {
    position: relative;
    overflow: inherit;
}

.section-5 .o-overlap-top {
    bottom: -15%;
    left: -25%;
    z-index: -10;
    width: 100%;
}

.section-5 .o-overlap-bottom {
    width: 50%;
    right: 20%;
    left: auto;
    top: 5%;
}

.section-6 .o-aspect-ratio {
    position: relative;
    overflow: inherit;
}

.section-6 .o-overlap-bottom {
    left: 30%;
    top: 0;
    right: 0;
    width: 100%;
    z-index: -10;
}

.section-6 .o-overlap-top {
    width: 50%;
    left: 20%;
    bottom: 0;
}

.section-6 .l-grid {
    align-items: stretch;
    grid-template-columns:minmax(400px, 50%) 1fr;
}

.section-7 .o-aspect-ratio {
    position: relative;
    overflow: inherit;
}

.section-7 .o-overlap-bottom {
    width: 100%;
    left: -15%;
}

.section-7 .o-overlap-top {
    width: 45%;
    left: 10%;
    bottom: 0;
}

.section-7 .l-grid {
    align-items: stretch;
}

.section-7 .box_area {
    width: 75%;
}

.section-8 .o-overlap-bottom {
    z-index: 10;
}

.section-9 .l-grid, .section-10 .l-grid {
    align-items: stretch;
    grid-template-columns:minmax(400px, 50%) 1fr;
}

.section-9 .o-aspect-ratio, .section-10 .o-aspect-ratio {
    position: relative;
    overflow: inherit;
}

.section-9 .o-overlap-top, .section-10 .o-overlap-top {
    bottom: -11%;
    left: -25%;
    z-index: -10;
    width: 100%;
}

.section-9 .o-overlap-bottom, .section-10 .o-overlap-bottom {
    width: 50%;
    right: 20%;
    left: auto;
    top: 0;
}

.section-11 {
    background: #fff;
    padding-bottom: 0;
}

.bg-move {
    border-radius: 100px;
    z-index: 10;
}

.bg-move.bg-none {
    background-image: none;
    background-color: unset;
}

.approach_footer_crest {
    position: absolute;
    bottom: 13%;
    right: 46%;
    width: auto; !important;
}

#badge {
    transform: scale(0.75);
    transition: transform 0.3s ease;
}

/* Desktop view: Disable horizontal scrolling */
@media only screen and (min-width: 768px) {
    .horizontal-scroll-container {
        overflow: visible;
    }

    .first {
        align-items: end;
    }

    .section {
        /*display: block;
        */
        width: 100%;
    }

    /* Hide arrows for desktop */
    #nextArrow, #PrevArrow {
        display: none;
    }
}

/* Mobile view: Enable carousel */
@media only screen and (max-width: 767px) {
    .horizontal-scroll-container {
        display: block;
    }

    #horizontal_scroll .header__inner__blk {
        padding: 0;
    }

    #horizontal_scroll .header__area {
        padding-top: 0;
    }

    .horizontal-scroll-container .owl-nav {
        display: block;
        width: 100%;
        position: fixed;
        top: calc(100vh - 90px);
    }

    #nextArrow, #PrevArrow {
        display: inline-block;
    }

    .horizontal-scroll-container .first .section .content-wrapper {
        margin: 0 auto;
        top: 15%;
        position: absolute;
    }

    .horizontal-line {
        display: none;
    }

    #horizontal_scroll .first .owl-stage {
        padding-top: 10em;
    }

    #horizontal_scroll .owl-stage {
        padding-top: 0 !important;
    }

    #horizontal_scroll button.owl-next {
        transform: rotate(180deg);
        float: right;
    }

    #horizontal_scroll button.owl-next img {
        width: 20px;
        left: 30px;
        position: relative;
        filter: brightness(0.8);
    }

    #horizontal_scroll button.owl-prev img {
        width: 20px;
        left: 30px;
        position: relative;
        filter: brightness(0.8);
    }

    #horizontal_scroll button.owl-next {
        margin-top: 15px;
    }

    #horizontal_scroll button.owl-prev {
        margin-top: 15px;
    }

    #horizontal_scroll .section {
        margin-top: 0;
    }

    .horizontal-scroll-container section {
        height: calc(100vh - 0px);
    }

    .l-center {
        min-height: 100%;
        overflow: initial;
    }

    #horizontal_scroll .section .content-wrapper.container {
        padding: 0 !important;
    }

    #horizontal_scroll .border {
        border: none !important;
    }

    .l-max {
        max-width: 100%;
        object-fit: contain;
        margin: 0;
        padding: 0;
        /*height: 250px;*/
    }

    .owl-carousel .first .section {
        display: flex;
        width: 100%;
        height: 80vw;
        justify-content: center;
        margin: auto;
        vertical-align: middle;
    }

    .l-grid {
        grid-template-columns: 100%;
        grid-gap: 0;
        flex-direction: column;
        margin-top: -2em;
    }

    .first .owl-nav {
        bottom: 0;
        position: relative;
    }

    .section-1 .o-aspect-ratio {
        position: relative;
        overflow: initial;
    }

    .section-2 {
        margin-top: -17px;
    }


    .section-2 .o-aspect-ratio {
        position: relative;
        overflow: initial;
    }

    .o-overlap-top {
        right: 0;
        top: -100%;
        bottom: auto;
    }

    .o-overlap-bottom {
        left: 60%;
        top: 35%;
        width: 40%;
        position: relative;
        right: 0;
        z-index: 10;
    }

    .section-2 .o-overlap-bottom {
        left: 0;
        top: -10px;
        width: 100%;
        position: relative;
        right: 0;
        z-index: -10;
    }

    .section-2 .o-overlap-top {
        position: absolute;
        width: 50%;
        left: 50%;
        padding-bottom: 0;
        right: 0;
        bottom: auto;
        top: -15%;
        z-index: 10;
    }

    .section.first.section-1.l-center {
        padding: 1em;
    }

    .section-3 .o-overlap-bottom {
        width: 100%;
        left: 0;
        top: 0;
        z-index: auto;
    }

    .section-3 .o-overlap-top {
        width: 50%;
        left: 50%;
        z-index: 10;
        margin-top: 40px;
    }

    .section-4 .l-grid {
        display: grid;
        grid-template-columns: 100%;
    }


    .section-4 .o-overlap-bottom {
        z-index: auto;
        width: 100%;
    }

    .section-4 .o-overlap-top {
        right: 0;
        top: -80%;
        bottom: auto;
        width: 40%;
        left: 60%;
        z-index: 10;
    }

    .section-4 .o-aspect-ratio {
        position: relative;
        overflow: inherit;
    }

    .section-5 .l-grid {
        align-items: stretch;
        grid-template-columns: 100%;
    }

    .section-5 .o-overlap-top {
        left: 0;
        z-index: auto;
        width: 100%;
    }

    .section-5 .o-overlap-bottom {
        width: 50%;
        right: 0;
        left: 50%;
        top: -15%;
    }

    .section-5 .box_area strong {
        line-height: 24px;
        display: none;
    }

    .section-5 {
        margin-top: 5px;
    }

    .section-5 .box_area h3 {
        margin-bottom: 0 !important;
    }

    .section-5 h3 br {
        display: none;
    }

    .section-6 .o-overlap-bottom {
        left: 0;
        top: 0;
        right: 0;
        width: 100%;
        z-index: auto;
    }

    .section-6 .o-aspect-ratio {
        position: relative;
        overflow: inherit;
    }

    .section-6 .o-overlap-top {
        width: 55%;
        left: 45%;
        bottom: 0;
        z-index: 10;
        margin-top: 40px;
    }


    .section-7 .box_area {
        width: 75%;
        margin-left: 0;
    }

    .section-7 p {
        font-size: 14px;
    }

    .section-7 .box_area strong {
        line-height: 24px;
        font-size: 18px;
    }

    .section-7 .o-overlap-bottom {
        width: 100%;
        left: 0;
        top: 0;
    }

    .section-7 .o-overlap-top {
        width: 50%;
        left: 50%;
        bottom: 0;
        z-index: 10;
        margin-top: -9em;
    }

    .section-9 .l-center {
        /*margin-top: 3em;*/
    }


    .section-9 .o-overlap-top {
        bottom: 0;
        left: 0;
        z-index: auto;
        width: 100%;
    }

    .section-9 .o-overlap-bottom {
        width: 50%;
        right: 0;
        left: 50%;
        top: -15%;
    }

    .section-7 .o-aspect-ratio[data-padding="66"]::before {
        padding-top: 0;
    }

    .bg-move {
        display: none;
        position: absolute;
        right: 5%;
        width: 150px;
        height: 150px;
        background-image: url(../images/sturge-toth-crest-white.svg);
        background-position: center;
        background-repeat: no-repeat;
        z-index: 0;
        margin-top: 0;
        background-color: unset;
    }

    .text-content blockquote:before {
        top: -8rem;
    }

    #badge {
        display: none;
    }

}

@media only screen and (min-width: 1000px) {
    .bg-move {
        position: absolute;
        top: 71%;
        right: 39%;
        width: 150px;
        /* Adjust this based on your desired size */
        height: 150px;
        background-image: url(../images/sturge-toth-crest-white.svg);
        background-position: center;
        background-repeat: no-repeat;
        cursor: pointer;
    }

    #badge.bg-move {
        position: fixed;
        right: unset;
        left: 0;
        top: 73%;
    }

    .bg-move:hover {
        cursor: pointer;
        /* Arrow cursor on hover */
    }

    #horizontal_scroll .owl-nav {
        display: none;
    }
}

@media only screen and (min-width: 1300px) {
    .section-3 .o-overlap-top {
        width: 45%;
        left: 15%;
        padding-bottom: 2em;
    }

    #horizontal_scroll .border {
        width: 75%;
    }
}

@media only screen and (max-width: 767px) and (min-width: 500px) {
    .section {
        height: 95vh;
    }

    .section-3 .o-overlap-bottom {
        width: 75%;
        left: 0;
        top: 0;
        z-index: auto;
    }

    .section-4 .o-overlap-bottom {
        z-index: auto;
        width: 65%;
    }

    .section-4 .o-overlap-bottom {
        z-index: auto;
        width: 80%;
    }

    .section-4 .o-overlap-top {
        display: none;
    }

    .section-6 .o-overlap-top {
        display: none;
    }

    .section-7 .o-overlap-top {
        display: none;
    }
}

@media (max-width: 999px) {
    #horizontal_scroll .owl-carousel .owl-item img {
        max-height: 100% !important;
        max-width: 100% !important;
    }
}

@media only screen and (max-width: 999px) and (min-width: 768px) {

    #horizontal_scroll .owl-carousel .owl-item img {
        max-height: 100% !important;
        max-width: 100% !important;
    }

   .l-grid {
        grid-template-columns: 100% !important;
    }

    .o-aspect-ratio {
            height: 300px;
    }

    .o-aspect-ratio__media {
        position: relative;
        height: 400px;
    }

    .horizontal-scroll-container .owl-nav {
        top: calc(100vh - 90px);
    }

    .l-center {
        min-height: 85vh;
    }

    .section-2 .box_area {
        /*width: 100% !important;*/
    }

    .content-wrapper {
        width: 110vw;
        height: 100%;
    }

    .section-2 .o-overlap-bottom {
        width: 100%;
    }

    .o-aspect-ratio__media {
        object-fit: cover;
    }

    .section-5 .o-overlap-top {
        bottom: 0;
        left: 0;
        z-index: -10;
        width: 100%;
    }

    .section-5 .o-overlap-bottom {
        width: 70%;
        right: 15%;
        left: auto;
        top: 80%;
    }

    .section-6 .o-overlap-top {
        width: 75%;
        left: 15%;
        bottom: 0;
    }

    .section-6 .o-overlap-bottom {
        left: 0;
        top: -40%;
    }

    .section-7 .o-overlap-bottom {
        width: 100%;
        left: 10%;
    }

    .section-7 .box_area {
        margin-left: 0;
    }

    .section-7 .o-overlap-top {
        width: 60%;
        left: 25%;
        bottom: -25%;
    }

    .section-2 .o-overlap-top {
        position: absolute;
        width: 70%;
        left: 10%;
        padding-bottom: 0;
    }

    .section-3 .o-aspect-ratio__media {
        object-fit: cover;
    }

    .horizontal-scroll-container section {
        height: calc(100vh - 100px);
        width: 110vh;
        margin-top: 85px;
        padding: 0 4em;
    }

    .l-grid {
        display: grid;
        grid-template-columns:minmax(400px, 50%) 1fr;
        grid-gap: 0;
        align-items: stretch;
    }

    #horizontal_scroll .box_area {
        width: 100%;
        padding: 2em;
    }

    #horizontal_scroll .box_area strong {
        font-size: 17px;
        color: #232323;
        margin-top: 10px;
        line-height: 20px;
        font-family: 'poppins', sans-serif;
        font-weight: 900;
    }

    #horizontal_scroll .box_area p {
        font-size: 15px;
    }

    #horizontal_scroll .box_area h3 {
        font-size: 15px;
    }

    #horizontal_scroll .first .owl-stage {
        padding-top: 10em;
    }

    #horizontal_scroll .owl-stage {
        padding-top: 0 !important;
    }

    #horizontal_scroll button.owl-next {
        transform: rotate(180deg);
        float: right;
    }

    #horizontal_scroll button.owl-next img {
        width: 40%;
    }

    #horizontal_scroll button.owl-prev img {
        width: 40%;
    }

    #horizontal_scroll button.owl-next {
        margin-top: 15px;
    }

    #horizontal_scroll button.owl-prev {
        margin-top: 15px;
    }

    #horizontal_scroll .section .content-wrapper.container {
        padding: 0 !important;
    }

    .section-1 .content-wrapper {
        height: auto;
        padding: 10px;
        flex-direction: column;
        align-items: flex-start;
    }

    .title {
        font-size: 70px;
        position: relative;
        top: 1%;
        /*bottom: auto;*/
        left: 3%;
        text-align: left;
    }

    .section {
        padding: 0 3em;
    }

    .horizontal-line {
        display: none;
    }

    .bg-move {
        position: absolute;
        top: 40%;
        right: 0;
        z-index: 10;
    }

    .section {
        padding: 20px;
        /* height: 100% !important;
        */
        height: 115vh;
    }
}

@media only screen and (max-width: 1500px) and (min-width: 1000px) {
    .section-2 .box_area {
        /*width: 95% !important;*/
    }

    .title {
        font-size: 64px;
        font-weight: 900;
        color: #eff3f7;
        position: absolute;
        top: 1em;
        left: 5em;
    }

    .header__btn a {
        padding: 14px 10px;
    }

    #horizontal_scroll .box_area {
        width: 75%;
    }

    #horizontal_scroll .box_area h3 {
        font-size: 18px;
        color: #c6cbcd;
        line-height: 1.1;
    }

    #horizontal_scroll .box_area strong {
        font-size: 24px !important;
        margin-top: 10px;
        line-height: 30px;
    }

    #horizontal_scroll .box_area p {
        font-size: 18px !important;
    }
}

@media only screen and (max-width: 1700px) and (min-width: 1500px) {
    #horizontal_scroll .box_area h3 {
        font-size: 25px;
        color: #c6cbcd;
        margin-bottom: 15px;
        font-family: 'picadilly', sans-serif;
        line-height: 1.1;
    }
}

@media only screen and (max-width: 1100px) and (min-width: 1000px) {

    .title {
        top: 1.5em !important;
        /* position: relative; */
    }

}


@media only screen and (max-width: 1100px) and (min-width: 768px) {
    #horizontal_scroll .box_area p {
        font-size: 14px !important;
    }

    min-width: 300px;

    .text-content {
        width: 85%;
        /*position: relative;*/
        top: 0;
        left: 0;
        /*width: 50%;*/
        /*top: 2em;*/
        /*left: -10.5em;*/
        /*@media (max-width: 999px) {*/
        /*    width: 70%;*/
        /*    position: absolute;*/
        /*    top: -2em;*/
        /*    left: 0;*/
        /*}*/
    }

    .o-aspect-ratio__media {
        position: absolute;
    }

    .section-3 .o-aspect-ratio__media, .section-4 .o-aspect-ratio__media {
        /*position: relative;*/
        position: absolute;
    }

    .title {
        font-size: 50px;
        top: -3.5em;
        letter-spacing: 45px;
    }

    .first {
        align-items: center;
    }
}




/* ----------------------------------------- HORIZONTAL SCROLL AREA END ------------------------ *