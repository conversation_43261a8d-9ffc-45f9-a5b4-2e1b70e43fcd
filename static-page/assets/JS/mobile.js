const burger = document.getElementById('burger');
const nav = document.getElementById('nav');
const close = document.getElementById('close');

burger.addEventListener('click', () => {
    nav.classList.toggle('active');
    burger.style.display = nav.classList.contains('active') ? 'none' : 'flex'; // Hide burger when menu is open
});

close.addEventListener('click', () => {
    nav.classList.remove('active');
    burger.style.display = 'flex'; // Show burger when menu is closed
});
