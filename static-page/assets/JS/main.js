(function ($) {
    "use strict";


    jQuery(document).ready(function ($) {




        //------------ Off<PERSON>vas menu -------------

        $('.open__menu').on('click', function () {
            $('.mobile__menu, .overlay').addClass('active');
        })
        $('.close__menu, .overlay').on('click', function () {
            $('.mobile__menu, .overlay').removeClass('active');
        })


        // exclusive__inner__blk
        $('.exclusive__inner__blk').owlCarousel({
            loop: true,
            nav: true,
            navText: ['<img src="/wp-content/plugins/landing-page_/static-page/assets/images/ST-Arrow-Left.svg" />', '<img src="/wp-content/plugins/landing-page_/static-page/assets/images/ST-Arrow-Left.svg" />'],
            dots: false,
            autoplay: false,
            smartSpeed: 1000,
            autoplayTimeout: 3500,
            items: 1,
            margin: 10,
            slideToScroll: 1,
            stagePadding: 250,
            center: true,
            autoplayHoverPause: true,
            responsive: {
                0: { stagePadding: 0 },
                320: { stagePadding: 40 },
                450: { stagePadding: 70 },
                575: { stagePadding: 100 },
                768: { stagePadding: 140 },
                992: { stagePadding: 180 },
                1200: { stagePadding: 200 },
                1360: { stagePadding: 250 }
            }
        });

        // Change cursor class on hover over navigation arrows
        $('.owl-nav .owl-prev').hover(
            function () {
                $('.exclusive__inner__blk').addClass('cursor-prev'); // Add cursor-prev class
            },
            function () {
                $('.exclusive__inner__blk').removeClass('cursor-prev'); // Remove cursor-prev class
            }
        );

        $('.owl-nav .owl-next').hover(
            function () {
                $('.exclusive__inner__blk').addClass('cursor-next'); // Add cursor-next class
            },
            function () {
                $('.exclusive__inner__blk').removeClass('cursor-next'); // Remove cursor-next class
            }
        );

        // Allow clicking the navigation arrows to change the slide
        // $('.owl-nav .owl-prev, .owl-nav .owl-next').on('click', function () {
        //     // Check which button was clicked and trigger appropriate action
        //     if ($(this).hasClass('owl-prev')) {
        //         $('.exclusive__inner__blk').trigger('prev.owl.carousel'); // Go to previous slide
        //     } else {
        //         $('.exclusive__inner__blk').trigger('next.owl.carousel'); // Go to next slide
        //     }
        // });

        // Handle active slide change
        $('.exclusive__inner__blk').on('changed.owl.carousel', function (event) {
            // Reset previous scales
            $('.owl-item').removeClass('scaled');
            // Apply the class to the active slide
            $('.owl-item').eq(event.item.index).addClass('scaled');
        });
    });
//===== Sticky Menu-Bar Start
    $(window).on('scroll', function (event) {
        var scroll = $(window).scrollTop();
        var stickyHeader = $(".header__area"); // Select the sticky header element

        if (scroll < 5) {
            // Remove the sticky class and reset slide-down effect
            stickyHeader.removeClass("sticky visible");
        } else {
            // Add the sticky class and trigger the slide-down effect
            stickyHeader.addClass("sticky");
            setTimeout(function () {
                stickyHeader.addClass("visible"); // Add 'visible' after sticky is added
            }, 10); // Small delay to ensure the 'sticky' class is applied before 'visible'
        }
    });
    //===== Sticky Menu-Bar End
    document.querySelectorAll('.accordion-item-jc-unique').forEach(item => {
        item.addEventListener('mouseenter', () => {
            // Close all items first
            document.querySelectorAll('.accordion-item-jc-unique').forEach(innerItem => {
                innerItem.classList.remove('active');
            });
            // Open the hovered item
            item.classList.add('active');
        });

        item.addEventListener('mouseleave', () => {
            // Keep the last hovered item active
            if (!item.classList.contains('active')) {
                item.classList.remove('active');
            }
        });
    });
    document.addEventListener('DOMContentLoaded', function () {
        // Get all studio areas
        const studioAreas = document.querySelectorAll('.studio__area');

        studioAreas.forEach(function (studioArea) {
            const studioContents = studioArea.querySelectorAll('.single__studio__contene');
            const studioThumbs = studioArea.querySelectorAll('.studio__thumb');

            // Function to handle image transitions
            function showImage(index) {
                studioThumbs.forEach((thumb, i) => {
                    if (i === index) {
                        // Show the selected image in the current section with fade-in
                        thumb.classList.add('active');
                    } else {
                        // Hide all other images within this section with fade-out
                        thumb.classList.remove('active');
                    }
                });
            }

            // Add click event listener to each content div
            studioContents.forEach((content, index) => {
                content.addEventListener('click', function () {
                    showImage(index);
                });
            });
        });
    });

    const burger = document.getElementById('burger');
    const nav = document.getElementById('nav');
    const logo = document.querySelector('.header__logo a img'); // Select the logo image
    const close = document.getElementById('close');

// Toggle the burger menu and add an ID to the logo when the menu is open
    burger.addEventListener('click', () => {
        nav.classList.toggle('active');

        if (nav.classList.contains('active')) {
            logo.id = 'menu-open-logo'; // Add ID when menu is open
        } else {
            logo.id = ''; // Remove ID when menu is closed
        }

        burger.style.display = nav.classList.contains('active') ? 'none' : 'flex'; // Toggle burger visibility
    });

// Close the menu and remove the ID from the logo
    close.addEventListener('click', () => {
        nav.classList.remove('active');
        logo.id = ''; // Ensure ID is removed when menu closes
        burger.style.display = 'flex'; // Show burger when menu is closed
    });

    jarallax(document.querySelectorAll('.accordion_bg'), {
        speed: 0.5,
        imgSize: 'cover'
    });

    AOS.init();
    window.addEventListener('load', AOS.refresh)

}(jQuery));