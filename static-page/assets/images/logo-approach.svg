<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="150.394" height="150.398" viewBox="0 0 150.394 150.398">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_846" data-name="Rectangle 846" width="150.394" height="150.398" fill="none" stroke="#fff" stroke-width="1"/>
    </clipPath>
  </defs>
  <g id="Group_1040" data-name="Group 1040" transform="translate(0 0)" opacity="0.37">
    <line id="Line_27" data-name="Line 27" y2="0.534" transform="translate(78.797 113.613)" fill="none" stroke="#232323" stroke-width="1"/>
    <path id="Path_919" data-name="Path 919" d="M390.63,163.325l.222.683h.718l-.579.419.222.683-.582-.423-.579.423.222-.683-.579-.419h.714l.222-.683" transform="translate(-269.033 -112.755)" fill="none" stroke="#232323" stroke-width="1"/>
    <path id="Path_920" data-name="Path 920" d="M304.452,163.325l.222.683h.718l-.579.419.219.683-.579-.423-.579.423.222-.683-.582-.419h.718l.222-.683" transform="translate(-209.536 -112.755)" fill="none" stroke="#232323" stroke-width="1"/>
    <path id="Path_921" data-name="Path 921" d="M368.881,155.79l.222.683h.718l-.579.423.219.68-.579-.42-.579.42.222-.68-.582-.423h.718l.222-.683" transform="translate(-254.016 -107.553)" fill="none" stroke="#232323" stroke-width="1"/>
    <path id="Path_922" data-name="Path 922" d="M323.047,155.79l.218.683h.718l-.579.423.222.68-.579-.42-.582.42.222-.68-.579-.423h.717l.222-.683" transform="translate(-222.374 -107.553)" fill="none" stroke="#232323" stroke-width="1"/>
    <path id="Path_923" data-name="Path 923" d="M345.815,151.57l.222.683h.717l-.579.42.222.683-.582-.423-.579.423.222-.683-.579-.42h.718l.218-.683" transform="translate(-238.094 -104.64)" fill="none" stroke="#232323" stroke-width="1"/>
    <line id="Line_28" data-name="Line 28" x1="0.208" y2="1.303" transform="translate(111.322 79.701)" fill="none" stroke="#232323" stroke-width="1"/>
    <line id="Line_29" data-name="Line 29" x1="1.536" y2="2.378" transform="translate(107.956 69.063)" fill="none" stroke="#232323" stroke-width="1"/>
    <g id="Group_1020" data-name="Group 1020" transform="translate(0 0)">
      <g id="Group_1019" data-name="Group 1019" clip-path="url(#clip-path)">
        <path id="Path_924" data-name="Path 924" d="M345.742,223.021a2.217,2.217,0,1,0-2.215,2.215A2.218,2.218,0,0,0,345.742,223.021Z" transform="translate(-235.629 -152.435)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_30" data-name="Line 30" y1="0.073" x2="3.061" transform="translate(99.162 71.823)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_31" data-name="Line 31" y1="0.101" x2="3.092" transform="translate(99.162 69.968)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_32" data-name="Line 32" x2="2.992" transform="translate(113.804 71.895)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_33" data-name="Line 33" y1="0.101" x2="3.092" transform="translate(113.804 69.968)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_34" data-name="Line 34" x1="0.024" y2="1.699" transform="translate(105.544 83.151)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_925" data-name="Path 925" d="M98.126,234.722c-.076-.083-.107-.208-.26-.229-.031.163.1.218.184.3C98.036,234.729,98.064,234.7,98.126,234.722Z" transform="translate(-67.561 -161.887)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_926" data-name="Path 926" d="M98.538,235.22c-.063-.017-.09.007-.076.073C98.49,235.269,98.514,235.241,98.538,235.22Z" transform="translate(-67.973 -162.385)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_927" data-name="Path 927" d="M115.615,184.481a.4.4,0,0,1,.211.066c.1.052.208.17.312.041a.309.309,0,0,0-.017-.416,1.78,1.78,0,0,1-.263-.4,5.769,5.769,0,0,1-.4-3.376c.087-.794.243-1.584.267-2.392a2.556,2.556,0,0,0-.62-1.931,4.273,4.273,0,0,0-1.868-.946c-.156-.052-.246.038-.288.177a1.53,1.53,0,0,0-.066.35.927.927,0,0,1-1.456.735,2.262,2.262,0,0,0-1.3-.347c-.118.011-.229.045-.26.156s.107.146.184.191c.35.208.762.274,1.116.5a3.794,3.794,0,0,1,1.6,2.09,19.119,19.119,0,0,1,.579,2.828,3.424,3.424,0,0,0,.461,1.577" transform="translate(-75.846 -120.891)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_928" data-name="Path 928" d="M146.308,191.443a2.1,2.1,0,0,1,.693.458c.3.412.749.426,1.158.537.142.041.374.076.423-.111.038-.163-.159-.257-.3-.34-.156-.094-.319-.177-.482-.267a.115.115,0,0,1-.062-.132c.021-.069.087-.049.135-.052a1.275,1.275,0,0,1,.461.142,2.162,2.162,0,0,0,1.539-.007.483.483,0,0,0,.295-.426c.028-.17-.135-.225-.257-.291-.551-.3-1.161-.44-1.737-.693a6.709,6.709,0,0,1-1.771-1.071,4.181,4.181,0,0,1-.995-1.348,2.5,2.5,0,0,1-.086-2.246.877.877,0,0,0-.118-1.06,3.336,3.336,0,0,1-.815-1.713,3.044,3.044,0,0,0-.392-1.379c-.184-.25-.371-.5-.541-.756a2.706,2.706,0,0,1-.5-.849,2.66,2.66,0,0,1-.17-1.165c0-.17,0-.267,0-.364a1.892,1.892,0,0,0-.264-1.012.878.878,0,0,0-.87-.485c-.028,0-.055-.024-.087-.038-.111-.045-.225-.09-.329.017s-.063.243-.007.357a2.922,2.922,0,0,1,.25.489,20.289,20.289,0,0,1,.683,2.971,23.159,23.159,0,0,1,.1,3.1c-.017.478.041.548.537.464a.74.74,0,0,1,.593.059.894.894,0,0,1,.2,1c-.1.329-.378.263-.613.277a1.416,1.416,0,0,1-.316-.052c-.26-.034-.447.121-.385.371a8.446,8.446,0,0,0,.686,2.364,7.849,7.849,0,0,0,1.186,1.594" transform="translate(-97.464 -122.005)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_929" data-name="Path 929" d="M131.013,186.817c.09.111.236.229.388.1.135-.114.049-.246-.017-.378a5.949,5.949,0,0,1-.43-.9c-.191-.579-.322-1.178-.506-1.757a15.988,15.988,0,0,1-.357-2.25c-.076-.464.014-.956-.083-1.414-.18-.86-.111-1.723-.208-2.583.045-.579-.031-.655-.621-.648a2.1,2.1,0,0,1-.673-.059c-.146-.045-.326-.232-.437-.08s.111.288.191.43a2.222,2.222,0,0,1,.461.974,20.391,20.391,0,0,1-.2,3.314,4.9,4.9,0,0,0,.243,2.978" transform="translate(-88.395 -122.045)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_930" data-name="Path 930" d="M92.253,264.387a7.373,7.373,0,0,1,1.633-2.4,6.089,6.089,0,0,1,2.419-1.4c.374-.1.752-.159,1.123-.25a6.181,6.181,0,0,1,3.3.163,2.775,2.775,0,0,1,.873.305,8.283,8.283,0,0,1,1.23.87,2.058,2.058,0,0,0,.326.3,1.126,1.126,0,0,0,1.13.042c.329-.153.291-.461.284-.749a.943.943,0,0,0-.19-.43,4.01,4.01,0,0,0-.9-.829,5.767,5.767,0,0,0-1.567-.9,6.628,6.628,0,0,0-1.837-.513,9.357,9.357,0,0,0-2.565.149,5.907,5.907,0,0,0-1.962.832,8.408,8.408,0,0,0-1.241.867,9.751,9.751,0,0,0-1.061,1.175,10.26,10.26,0,0,0-.853,1.314c-.118.232-.194.475-.312.7a6.607,6.607,0,0,0-.537,2.059c-.011.087-.011.187.1.232.167-.149.011-.4.146-.52C92.129,265.143,92.087,264.723,92.253,264.387Z" transform="translate(-63.203 -178.519)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_931" data-name="Path 931" d="M109.768,188.325a2.739,2.739,0,0,1-1.518-2.076c-.263-1.088-.3-2.225-.62-3.314a3.665,3.665,0,0,0-1.043-1.806,3.286,3.286,0,0,0-1.449-.728c-.291-.069-.392.059-.319.347.028.118.049.232.069.34-.049.3-.052.579-.416.763-.44.225-.856.052-1.279.087-.107.014-.222-.038-.274.08s.094.152.159.2a1.3,1.3,0,0,0,.308.163,4.463,4.463,0,0,1,2.271,2.27c.374.745.655,1.536,1.009,2.291a5.631,5.631,0,0,0,.7,1.217,3.592,3.592,0,0,0,1.508,1.112c.43.163.475.177.735-.194" transform="translate(-71.043 -124.531)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_932" data-name="Path 932" d="M105.3,193.653a4.679,4.679,0,0,1-.974-1.109c-.333-.568-.541-1.213-.818-1.813a13.028,13.028,0,0,0-1.1-2.173,4.733,4.733,0,0,0-2.492-1.64.262.262,0,0,0-.3.052c-.09.1-.011.2.045.291.125.225.267.444.388.669a.74.74,0,0,1-.326,1.12.554.554,0,0,0-.159.107c-.08.076-.1.215,0,.242.329.076.538.361.846.454a1.71,1.71,0,0,1,.721.472c.711.7,1.4,1.421,2.076,2.159a4.749,4.749,0,0,0,1.4,1.251c.555.26,1.009.745,1.671.794a.286.286,0,0,1,.173.076.7.7,0,0,0,.457.135c.094.007.222.038.25-.076.035-.139-.107-.156-.2-.2" transform="translate(-68.693 -129.026)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_933" data-name="Path 933" d="M137.9,184.974a1.634,1.634,0,0,0-.281-.842,5.5,5.5,0,0,1-.86-2.926c-.052-1.075,0-2.156-.031-3.238a14.088,14.088,0,0,0-.26-2.776c-.121-.534-.284-1.057-.444-1.577-.284-.95-.915-1.744-1.189-2.693a.352.352,0,0,0-.385-.229,1.153,1.153,0,0,1-.347,0c-.274-.069-.222.073-.159.225.159.4.385.794.128,1.248a.455.455,0,0,0,.031.281,20.534,20.534,0,0,1,.423,3.116c.062.555.045,1.12.125,1.667a22.761,22.761,0,0,0,.582,3.151c.163.541.346,1.082.544,1.609a8.553,8.553,0,0,0,.947,1.716" transform="translate(-92.44 -117.831)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_934" data-name="Path 934" d="M119.141,230.382a6.938,6.938,0,0,0-.9-1.806,20.516,20.516,0,0,0-1.286-1.8,9.185,9.185,0,0,0-.783-.777,15.465,15.465,0,0,0-1.765-1.383c-.423-.3-.929-.492-1.338-.811a10.119,10.119,0,0,0-1.851-.988c-.312-.153-.683-.246-1-.388-.523-.236-1.088-.312-1.6-.541a8.245,8.245,0,0,0-1.487-.416c-.572-.132-1.123-.329-1.688-.5-.648-.191-1.286-.392-1.927-.607-.721-.246-1.4-.582-2.118-.843-.385-.138-.745-.4-1.123-.579-.738-.35-1.376-.86-2.073-1.276-.461-.277-.839-.7-1.279-1.023a6.233,6.233,0,0,1-1.508-1.809,2.107,2.107,0,0,1-.43-1.008c-.011-.243-.267-.433-.236-.721.021-.187.017-.295.253-.159.2.111.294.308.454.44.333.281.676.558.995.849a19.435,19.435,0,0,0,2,1.244,16.3,16.3,0,0,0,2.2,1.1c.711.357,1.456.62,2.187.925.78.326,1.6.548,2.357.908.538.26,1.109.378,1.647.61s1.075.406,1.6.638a7.874,7.874,0,0,0,.808.274c.686.242,1.31.624,1.986.887a22.126,22.126,0,0,1,2.624,1.328,8.027,8.027,0,0,1,.891.6c.291.218.638.374.915.62.437.388.873.773,1.31,1.165a14.936,14.936,0,0,1,1.172,1.269,4.463,4.463,0,0,1,.707,1.043c.253.544.53,1.075.783,1.615a7.643,7.643,0,0,1,.3,1.144,7.32,7.32,0,0,1,.121,3.411c-.069.368-.087.745-.125,1.12a.286.286,0,0,1-.1.2.4.4,0,0,1-.1-.353A11.717,11.717,0,0,0,119.141,230.382Z" transform="translate(-65.409 -146.974)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_935" data-name="Path 935" d="M108.793,204.432c-.766-.194-2.4-.832-3.168-1.04-.378-.1-.78-.263-1.165-.392-.912-.3-1.834-.558-2.749-.842-.617-.194-1.244-.385-1.844-.617-.624-.243-1.258-.475-1.868-.763-.52-.243-1.04-.492-1.56-.731a7.891,7.891,0,0,1-.96-.6,8.622,8.622,0,0,1-1.054-.724c-.444-.4-.953-.7-1.376-1.113-.083-.083-.191-.167-.139-.3s.187-.145.326-.153c.693-.014.693-.017.392-.659-.173-.371-.447-.676-.61-1.054-.052-.128-.111-.239-.038-.364a.274.274,0,0,1,.326-.076,6.748,6.748,0,0,1,2.673,1.05,13.189,13.189,0,0,1,2.184,2.146,9.847,9.847,0,0,0,2.718,2.163,4.373,4.373,0,0,0,1.116.35,3.491,3.491,0,0,1,1.872.79,3.512,3.512,0,0,0,1.422.877c1.043.4,2.121.717,3.127,1.21.787.381,1.574.773,2.326,1.217a2.231,2.231,0,0,1,1.054.884,3.422,3.422,0,0,1-1.082-.406A15.3,15.3,0,0,0,108.793,204.432Z" transform="translate(-64.133 -134.613)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_936" data-name="Path 936" d="M100.056,187.929c-.121-.225-.264-.444-.388-.669-.055-.087-.135-.187-.045-.291a.26.26,0,0,1,.3-.052,4.734,4.734,0,0,1,2.493,1.64,13.043,13.043,0,0,1,1.1,2.174c.277.6.485,1.244.818,1.813a4.659,4.659,0,0,0,.974,1.109,6.927,6.927,0,0,0,1.667.815c.1.042.239.059.2.2-.028.114-.156.083-.25.076a.7.7,0,0,1-.457-.135.285.285,0,0,0-.174-.076c-.662-.049-1.116-.534-1.671-.794a4.752,4.752,0,0,1-1.4-1.251c-.679-.738-1.366-1.46-2.076-2.16a1.712,1.712,0,0,0-.721-.472c-.309-.094-.516-.378-.846-.454-.107-.028-.083-.167,0-.243a.56.56,0,0,1,.159-.107A.74.74,0,0,0,100.056,187.929Z" transform="translate(-68.693 -129.026)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_937" data-name="Path 937" d="M104.818,180.748c-.073-.288.028-.416.319-.347a3.286,3.286,0,0,1,1.449.728,3.665,3.665,0,0,1,1.043,1.806c.316,1.089.357,2.226.62,3.314a2.739,2.739,0,0,0,1.518,2.076c.218.09.263.236.1.43-.09.107-.184.215-.263.326-.26.371-.3.357-.735.194a3.592,3.592,0,0,1-1.508-1.112,5.631,5.631,0,0,1-.7-1.217c-.354-.755-.634-1.546-1.009-2.291a4.463,4.463,0,0,0-2.271-2.27,1.3,1.3,0,0,1-.308-.163c-.066-.052-.218-.073-.159-.2s.166-.066.274-.08c.423-.035.839.139,1.279-.087.364-.184.368-.458.416-.763C104.867,180.98,104.846,180.866,104.818,180.748Z" transform="translate(-71.043 -124.531)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_938" data-name="Path 938" d="M130.008,180.211c.1.458.007.95.083,1.414a15.99,15.99,0,0,0,.357,2.249c.184.579.316,1.179.506,1.757a5.981,5.981,0,0,0,.43.9c.066.132.152.263.017.378-.153.132-.3.014-.388-.1a1.482,1.482,0,0,0-.516-.371,4.206,4.206,0,0,1-1.737-1.91,4.892,4.892,0,0,1-.243-2.978,20.391,20.391,0,0,0,.2-3.314,2.223,2.223,0,0,0-.461-.974c-.08-.142-.3-.277-.191-.43s.291.035.437.08a2.1,2.1,0,0,0,.673.059c.59-.007.666.069.62.648C129.9,178.488,129.828,179.351,130.008,180.211Z" transform="translate(-88.395 -122.045)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_939" data-name="Path 939" d="M142.964,179.837a2.707,2.707,0,0,0,.5.849c.17.256.357.506.541.755a3.038,3.038,0,0,1,.392,1.38,3.339,3.339,0,0,0,.815,1.713.877.877,0,0,1,.118,1.06,2.5,2.5,0,0,0,.087,2.246,4.171,4.171,0,0,0,.995,1.348,6.7,6.7,0,0,0,1.771,1.071c.575.253,1.186.388,1.737.693.121.066.284.121.256.291a.483.483,0,0,1-.295.426,2.161,2.161,0,0,1-1.539.007,1.279,1.279,0,0,0-.461-.142c-.049,0-.115-.017-.135.052a.115.115,0,0,0,.063.132c.163.09.326.173.482.267.139.083.336.177.3.34-.049.187-.281.152-.423.111-.409-.111-.856-.125-1.158-.537a2.1,2.1,0,0,0-.694-.458,10.576,10.576,0,0,1-2.149-1.661,7.847,7.847,0,0,1-1.185-1.594,8.426,8.426,0,0,1-.686-2.364c-.063-.25.125-.406.385-.371a1.413,1.413,0,0,0,.315.052c.236-.014.51.052.614-.277a.9.9,0,0,0-.2-1,.742.742,0,0,0-.593-.059c-.5.083-.555.014-.537-.464a23.111,23.111,0,0,0-.1-3.1,20.244,20.244,0,0,0-.683-2.971,2.986,2.986,0,0,0-.25-.489c-.055-.114-.1-.242.007-.357s.218-.062.329-.017c.031.014.059.042.087.038a.879.879,0,0,1,.87.486,1.889,1.889,0,0,1,.263,1.012c0,.1,0,.194,0,.364A2.668,2.668,0,0,0,142.964,179.837Z" transform="translate(-97.465 -122.005)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_940" data-name="Path 940" d="M136.472,175.192a14.088,14.088,0,0,1,.26,2.776c.031,1.082-.021,2.163.031,3.238a5.5,5.5,0,0,0,.86,2.926,1.634,1.634,0,0,1,.281.842,3.66,3.66,0,0,1-1.175-1.265,8.558,8.558,0,0,1-.946-1.716c-.2-.527-.381-1.068-.544-1.608a22.818,22.818,0,0,1-.582-3.151c-.08-.548-.063-1.113-.125-1.667a20.53,20.53,0,0,0-.423-3.116.454.454,0,0,1-.031-.281c.257-.454.031-.846-.128-1.248-.063-.152-.115-.294.159-.225a1.161,1.161,0,0,0,.346,0,.353.353,0,0,1,.385.229c.274.95.9,1.744,1.189,2.693C136.188,174.134,136.351,174.658,136.472,175.192Z" transform="translate(-92.44 -117.831)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_941" data-name="Path 941" d="M115.731,178c-.024.808-.18,1.6-.267,2.392a5.769,5.769,0,0,0,.4,3.376,1.79,1.79,0,0,0,.263.4.309.309,0,0,1,.017.416c-.1.128-.208.011-.312-.041a.4.4,0,0,0-.211-.066,2.13,2.13,0,0,1-1.806-1.1,3.423,3.423,0,0,1-.461-1.577,19.119,19.119,0,0,0-.579-2.828,3.792,3.792,0,0,0-1.6-2.09c-.354-.222-.766-.288-1.116-.5-.076-.045-.215-.066-.184-.191s.142-.146.26-.156a2.263,2.263,0,0,1,1.3.347.927.927,0,0,0,1.456-.735,1.513,1.513,0,0,1,.066-.35c.041-.139.132-.229.288-.177a4.275,4.275,0,0,1,1.868.946A2.557,2.557,0,0,1,115.731,178Z" transform="translate(-75.847 -120.891)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_942" data-name="Path 942" d="M92.87,204.978c.076-.052.159.028.229.08.627.478,1.23,1,1.9,1.425a22.263,22.263,0,0,0,3.4,1.9c.839.354,1.66.763,2.523,1.043.572.184,1.13.412,1.705.568.641.18,1.255.444,1.9.607,1.04.263,2.052.624,3.071.957a13.283,13.283,0,0,1,2.024.78,10.944,10.944,0,0,0,1.123.461,11.742,11.742,0,0,1,1.463.735c.364.2.731.458,1.088.7a23.277,23.277,0,0,1,1.9,1.6,7.818,7.818,0,0,1,.887,1.071,15.233,15.233,0,0,1,1.716,3.037,2.08,2.08,0,0,1,.076.215c-.007.094.011.17-.066.2-.055.021-.094-.035-.135-.066a1.854,1.854,0,0,1-.354-.454,10.632,10.632,0,0,0-1.272-1.317,14.156,14.156,0,0,0-2.388-1.779,29.279,29.279,0,0,0-2.929-1.6c-.88-.43-1.8-.769-2.693-1.172-.562-.25-1.154-.454-1.74-.669-1.179-.433-2.347-.895-3.525-1.321q-1.424-.515-2.815-1.116a22.7,22.7,0,0,1-2.1-.967,14.764,14.764,0,0,1-3.754-2.746,8.41,8.41,0,0,1-1.241-1.9C92.815,205.155,92.78,205.041,92.87,204.978Z" transform="translate(-64.076 -141.499)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_943" data-name="Path 943" d="M94.572,273.175a7.128,7.128,0,0,1,.527-2.243,3.911,3.911,0,0,1,.662-1.158c.208-.253.326-.568.548-.818a7.988,7.988,0,0,1,1.723-1.518,4.989,4.989,0,0,1,1.886-.565,7.62,7.62,0,0,1,1.927,0,4.311,4.311,0,0,1,.932.253,2.145,2.145,0,0,0,.555.146c.159,0,.239.125.3.309a7.734,7.734,0,0,0-2.139.028,4.964,4.964,0,0,0-2.454,1.491,5.325,5.325,0,0,0-1.075,1.9,5.088,5.088,0,0,0,.021,3.227,5.548,5.548,0,0,0,.953,1.875,7.065,7.065,0,0,0,2.912,2.25,7.455,7.455,0,0,0,2.388.478,6.088,6.088,0,0,0,3.321-.735,6.309,6.309,0,0,0,2.312-1.854,8.264,8.264,0,0,0,.835-1.137,7.417,7.417,0,0,0,.61-1.4c.021-.09.007-.257.163-.219.118.024.1.167.1.277a8.743,8.743,0,0,1-.444,2.066,5.916,5.916,0,0,1-1.1,2.166,4.819,4.819,0,0,1-1.31,1.314c-.208.121-.367.322-.568.468a7.35,7.35,0,0,1-2.856,1.248,7.193,7.193,0,0,1-3.283.066c-.489-.125-1-.163-1.483-.3a8.161,8.161,0,0,1-1.359-.565,9.411,9.411,0,0,1-1.557-.9,7.922,7.922,0,0,1-2.87-3.931A6.9,6.9,0,0,1,94.572,273.175Z" transform="translate(-65.282 -184.197)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_944" data-name="Path 944" d="M99.763,257.84c.132-.149.191.132.312.166.724.211,1.314.714,2.035.929.42.125.856.215,1.272.333a7.467,7.467,0,0,0,2.523.173,9.72,9.72,0,0,0,2.94-.662,9.051,9.051,0,0,0,2.839-1.74,5.935,5.935,0,0,0,1.439-1.661c.25-.489.572-.946.818-1.442a7.2,7.2,0,0,0,.693-2.257,11.572,11.572,0,0,0,.142-1.733,9.739,9.739,0,0,0-.5-2.9,9.439,9.439,0,0,0-1.317-2.628,13.134,13.134,0,0,0-1.043-1.286,13.366,13.366,0,0,0-2.465-2.045c-.44-.284-.922-.492-1.387-.735a23.248,23.248,0,0,0-3.193-1.29c-.655-.236-1.321-.444-1.962-.714a13.392,13.392,0,0,1-2.61-1.394,12.9,12.9,0,0,1-2.135-1.9.407.407,0,0,1-.111-.26c-.083-.083-.215-.139-.184-.3.153.021.184.146.26.229a5.128,5.128,0,0,1,1.161.79,6.958,6.958,0,0,0,1.04.707,17.548,17.548,0,0,0,2.239,1.088c1.331.53,2.7.967,4.066,1.411a26.988,26.988,0,0,1,2.687.981,13.352,13.352,0,0,1,2.01,1.126,16.474,16.474,0,0,1,2.253,1.705,12.911,12.911,0,0,1,2.132,2.569,10.422,10.422,0,0,1,1.466,3.7,11.767,11.767,0,0,1,.177,1.595,8.188,8.188,0,0,1-.277,2.357,9.888,9.888,0,0,1-.718,2.094,10.432,10.432,0,0,1-2.128,3.04,14.1,14.1,0,0,1-2.028,1.525,11.289,11.289,0,0,1-2.51,1.06,5.636,5.636,0,0,1-1.491.225,12.5,12.5,0,0,1-3.706-.35,12.922,12.922,0,0,1-4.371-2.045.409.409,0,0,1-.146-.163C99.933,258.013,99.659,257.961,99.763,257.84Z" transform="translate(-67.561 -161.888)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_945" data-name="Path 945" d="M110.476,247.971c.43-.319.79-.714,1.2-1.054a6.439,6.439,0,0,0,.638-.8,9.454,9.454,0,0,0,1.56-2.534,13.024,13.024,0,0,0,.565-1.636,18.421,18.421,0,0,0,.388-2.274,5.126,5.126,0,0,0-.094-1.892c-.118-.486-.156-.991-.288-1.477a14.022,14.022,0,0,0-.631-1.685,8.287,8.287,0,0,0-1.12-1.813,8.124,8.124,0,0,0-.984-1.126c-.371-.326-.7-.711-1.075-1.05-.51-.464-1.082-.842-1.629-1.255a15.963,15.963,0,0,0-2.246-1.283,20.039,20.039,0,0,0-2.049-.846,12.314,12.314,0,0,0-1.82-.655c-.6-.132-1.154-.381-1.737-.548a13.186,13.186,0,0,1-1.8-.766,18.674,18.674,0,0,1-2.274-1.23,9.447,9.447,0,0,1-1.764-1.387,5.333,5.333,0,0,1-1.3-1.823,3.111,3.111,0,0,1-.316-1.844,2.546,2.546,0,0,0,.014-.61c0-.107-.017-.246.118-.267a.277.277,0,0,1,.333.19,6.059,6.059,0,0,0,1.258,1.924,5.872,5.872,0,0,0,.829.8,12.353,12.353,0,0,0,1.993,1.47c.707.406,1.418.8,2.156,1.151a24.934,24.934,0,0,0,2.915,1.238,9.832,9.832,0,0,0,2.149.627c.506.052.988.346,1.5.426.468.076.87.336,1.31.437a6.608,6.608,0,0,1,1.577.541,15.045,15.045,0,0,1,2.284,1.134c.416.295.9.482,1.331.756a7.781,7.781,0,0,1,1.335,1.054c.3.308.614.6.929.887a3.114,3.114,0,0,1,.451.537,9.61,9.61,0,0,1,1.359,2.174,11.558,11.558,0,0,1,.683,2.229,9.871,9.871,0,0,1,.115,2.021,10.917,10.917,0,0,1-.478,2.8,18.674,18.674,0,0,1-1.3,2.784c-.211.4-.5.745-.718,1.134-.062.114-.215.173-.312.274-.53.544-1.113,1.033-1.674,1.546-.35.319-.794.485-1.168.777a12.4,12.4,0,0,1-1.359.846,1.861,1.861,0,0,1-.745.347c-.066.007-.163.035-.2-.052A.165.165,0,0,1,110.476,247.971Z" transform="translate(-64.654 -150.576)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_946" data-name="Path 946" d="M75.544.5a75.044,75.044,0,1,0,75.041,75.044A75.041,75.041,0,0,0,75.544.5Z" transform="translate(-0.345 -0.345)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_947" data-name="Path 947" d="M84.726,155.649a70.924,70.924,0,1,1,70.919-70.922A70.926,70.926,0,0,1,84.726,155.649Z" transform="translate(-9.527 -9.527)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_948" data-name="Path 948" d="M239.79,182.918l.014-1.127c-.059-.1-.125-.187-.184-.284Z" transform="translate(-165.427 -125.307)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_949" data-name="Path 949" d="M329.62,81.916l14.334-15.509c-1.685-1.466-3.453-2.846-5.279-4.142L324.268,82.651A35.77,35.77,0,0,1,329.62,81.916Z" transform="translate(-223.865 -42.986)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_950" data-name="Path 950" d="M43.23,151.742l-4.967-2.288c-.936,2.031-1.789,4.111-2.53,6.246l3.092.957A36.834,36.834,0,0,1,43.23,151.742Z" transform="translate(-24.668 -103.179)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_951" data-name="Path 951" d="M414.76,136.769l4.953-2.128q-1.456-3.078-3.2-5.976l-7.085,4.215A36.354,36.354,0,0,1,414.76,136.769Z" transform="translate(-282.66 -88.826)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_952" data-name="Path 952" d="M24.811,215.1c-.069.572-.149,1.144-.208,1.726.111-.579.236-1.144.371-1.705Z" transform="translate(-16.985 -148.501)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_953" data-name="Path 953" d="M346.559,91.555a36.2,36.2,0,0,1,4.7.333l12.378-10.857q-2.3-2.459-4.825-4.683L345.439,91.593C345.813,91.583,346.181,91.555,346.559,91.555Z" transform="translate(-238.481 -52.708)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_954" data-name="Path 954" d="M250.053,22.087q-3.25-.312-6.586-.316H243.4l.208,15.973a11.194,11.194,0,0,1,4.461,1.064Z" transform="translate(-168.039 -15.03)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_955" data-name="Path 955" d="M245.191,157.937l1.494-12.614a11.913,11.913,0,0,1-1.657.215Z" transform="translate(-169.16 -100.327)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_956" data-name="Path 956" d="M264.632,43.413l4.413-19.5q-3.245-.655-6.586-1l-1.567,17.072A11.355,11.355,0,0,1,264.632,43.413Z" transform="translate(-180.112 -15.818)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_957" data-name="Path 957" d="M251.757,152.405c.184-.277.374-.548.568-.818l2.35-10.406a10.927,10.927,0,0,1-1.972.9Z" transform="translate(-173.806 -97.467)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_958" data-name="Path 958" d="M262.24,60.848l-1.74,8.746q.723-.832,1.5-1.622l13.564-39.954q-3.172-.983-6.458-1.667L265.02,46.871a11.265,11.265,0,0,1-2.78,13.976Z" transform="translate(-179.842 -18.191)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_959" data-name="Path 959" d="M394.8,119.478l7.463-4.181q-1.763-2.9-3.789-5.6l-9.463,7.068A36.147,36.147,0,0,1,394.8,119.478Z" transform="translate(-268.562 -75.733)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_960" data-name="Path 960" d="M373.382,104.432l9.973-7.047c-1.363-1.792-2.815-3.512-4.34-5.162l-11.623,10.746A35.8,35.8,0,0,1,373.382,104.432Z" transform="translate(-253.637 -63.668)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_961" data-name="Path 961" d="M34.208,171.593l-2.95-1a66.839,66.839,0,0,0-1.851,6.492l1.373.274A35.807,35.807,0,0,1,34.208,171.593Z" transform="translate(-20.302 -117.771)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_962" data-name="Path 962" d="M309.1,74.638l15.64-20.944q-2.735-1.924-5.664-3.577l-14.6,26.071A35.707,35.707,0,0,1,309.1,74.638Z" transform="translate(-210.201 -34.599)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_963" data-name="Path 963" d="M290.668,69.959l16.067-27.007c-1.944-1.085-3.938-2.083-5.99-2.978L287.011,71.948A36.136,36.136,0,0,1,290.668,69.959Z" transform="translate(-198.144 -27.597)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_964" data-name="Path 964" d="M274.815,67.815l15.419-33.444q-3.057-1.311-6.264-2.323L272.222,69.892C273.057,69.164,273.914,68.463,274.815,67.815Z" transform="translate(-187.934 -22.126)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_965" data-name="Path 965" d="M27.855,192.858l-1.293-.291q-.749,3.27-1.175,6.666l.208.021A35.878,35.878,0,0,1,27.855,192.858Z" transform="translate(-17.526 -132.943)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_966" data-name="Path 966" d="M136.136,76.126,121.927,50.755q-2.9,1.659-5.612,3.595l15.259,20.434A36.135,36.135,0,0,1,136.136,76.126Z" transform="translate(-80.3 -35.04)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_967" data-name="Path 967" d="M154.659,71.608,141.313,40.546c-2.035.9-4.028,1.9-5.955,2.988l15.613,26.237A36.558,36.558,0,0,1,154.659,71.608Z" transform="translate(-93.447 -27.992)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_968" data-name="Path 968" d="M68.271,117.51l-9.46-7.061q-2,2.683-3.733,5.571l7.567,4.236A35.609,35.609,0,0,1,68.271,117.51Z" transform="translate(-38.024 -76.251)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_969" data-name="Path 969" d="M173.2,69.15,161.822,32.507q-3.187,1.03-6.222,2.357L170.506,67.2Q171.9,68.114,173.2,69.15Z" transform="translate(-107.422 -22.442)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_970" data-name="Path 970" d="M117.907,82.963,103.8,63q-2.714,1.95-5.22,4.153l14.08,15.235A35.779,35.779,0,0,1,117.907,82.963Z" transform="translate(-68.058 -43.495)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_971" data-name="Path 971" d="M100.1,92.089c.049,0,.1.007.142.007L87.11,77.121q-2.506,2.22-4.783,4.669L94.56,92.515A36.913,36.913,0,0,1,100.1,92.089Z" transform="translate(-56.836 -53.242)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_972" data-name="Path 972" d="M83.554,103.6,72.042,92.962q-2.267,2.465-4.281,5.148l9.983,7.058A36.006,36.006,0,0,1,83.554,103.6Z" transform="translate(-46.78 -64.178)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_973" data-name="Path 973" d="M54.71,133.62l-7.2-4.284q-1.716,2.886-3.151,5.938l5.2,2.236A36.556,36.556,0,0,1,54.71,133.62Z" transform="translate(-30.627 -89.29)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_974" data-name="Path 974" d="M185.978,52.381a11.223,11.223,0,0,1,1.262-5.179l-4.083-20.528c-2.184.461-4.33,1.033-6.43,1.7l13.051,38.449q.879.806,1.7,1.664l-1.494-7.5A11.23,11.23,0,0,1,185.978,52.381Z" transform="translate(-122.006 -18.415)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_975" data-name="Path 975" d="M226.454,149.685c.288.354.558.725.832,1.088l-.811-8.836a11.178,11.178,0,0,1-1.979-.925Z" transform="translate(-154.985 -97.351)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_976" data-name="Path 976" d="M206.7,40.143l-1.567-17.062c-2.226.239-4.416.589-6.573,1.036l4.413,19.5A11.329,11.329,0,0,1,206.7,40.143Z" transform="translate(-137.077 -15.934)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_977" data-name="Path 977" d="M227.511,21.794c-2.243.014-4.461.139-6.642.36l1.972,16.7a11.188,11.188,0,0,1,4.458-1.1Z" transform="translate(-152.481 -15.046)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_978" data-name="Path 978" d="M236.661,156.488c.059.1.125.187.184.284l.146-11.28a11.368,11.368,0,0,1-1.657-.225Z" transform="translate(-162.467 -100.288)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_979" data-name="Path 979" d="M245.515,299.706l-.017,1.13c.059.1.125.19.184.284Z" transform="translate(-169.485 -206.908)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_980" data-name="Path 980" d="M112.2,358.418,97.865,373.927q2.532,2.2,5.283,4.142l14.4-20.382A36.208,36.208,0,0,1,112.2,358.418Z" transform="translate(-67.563 -246.937)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_981" data-name="Path 981" d="M425.765,317.97l4.967,2.288a66.641,66.641,0,0,0,2.531-6.243l-3.092-.964A36.421,36.421,0,0,1,425.765,317.97Z" transform="translate(-293.936 -216.121)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_982" data-name="Path 982" d="M48.037,330.941l-4.95,2.128a66.643,66.643,0,0,0,3.193,5.976l7.089-4.215A36.384,36.384,0,0,1,48.037,330.941Z" transform="translate(-29.746 -228.472)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_983" data-name="Path 983" d="M98.581,359.583a36.906,36.906,0,0,1-4.7-.329L81.5,370.107q2.3,2.459,4.829,4.687L99.7,359.545C99.322,359.556,98.958,359.583,98.581,359.583Z" transform="translate(-56.264 -248.018)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_984" data-name="Path 984" d="M37.125,313.812l-2.77.863a66.711,66.711,0,0,0,2.569,6.288l4.735-2.184A36.266,36.266,0,0,1,37.125,313.812Z" transform="translate(-23.718 -216.647)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_985" data-name="Path 985" d="M61.364,344.5,53.9,348.676q1.763,2.9,3.789,5.6l9.463-7.068A36.12,36.12,0,0,1,61.364,344.5Z" transform="translate(-37.212 -237.832)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_986" data-name="Path 986" d="M76.749,354.127l-9.973,7.051c1.359,1.792,2.811,3.508,4.34,5.155l11.619-10.739A36.14,36.14,0,0,1,76.749,354.127Z" transform="translate(-46.1 -244.479)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_987" data-name="Path 987" d="M131.43,353.011l-15.64,20.947q2.735,1.919,5.664,3.574l14.6-26.071A36.053,36.053,0,0,1,131.43,353.011Z" transform="translate(-79.938 -242.639)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_988" data-name="Path 988" d="M151.079,344.53l-16.067,27.007c1.941,1.085,3.934,2.083,5.986,2.977l13.737-31.974A36.187,36.187,0,0,1,151.079,344.53Z" transform="translate(-93.208 -236.48)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_989" data-name="Path 989" d="M170.746,333.6,155.331,367.04c2.035.877,4.129,1.647,6.26,2.323l11.748-37.839C172.507,332.251,171.648,332.948,170.746,333.6Z" transform="translate(-107.236 -228.874)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_990" data-name="Path 990" d="M24.6,277.517c.26,1.678.592,3.331.967,4.964l.895-.2A36.457,36.457,0,0,1,24.6,277.517Z" transform="translate(-16.986 -191.59)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_991" data-name="Path 991" d="M305.393,353.085,319.6,378.459c1.931-1.109,3.8-2.316,5.609-3.6l-15.256-20.431A35.8,35.8,0,0,1,305.393,353.085Z" transform="translate(-210.834 -243.759)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_992" data-name="Path 992" d="M288.018,344.913l13.346,31.062q3.057-1.357,5.955-2.988L291.71,346.754A37.407,37.407,0,0,1,288.018,344.913Z" transform="translate(-198.839 -238.118)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_993" data-name="Path 993" d="M388.037,346.4l9.456,7.061q2-2.683,3.733-5.571l-7.564-4.236A35.937,35.937,0,0,1,388.037,346.4Z" transform="translate(-267.889 -237.252)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_994" data-name="Path 994" d="M273.274,334.927l11.377,36.643q3.182-1.035,6.226-2.357l-14.906-32.338Q274.572,335.965,273.274,334.927Z" transform="translate(-188.66 -231.224)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_995" data-name="Path 995" d="M324.716,358.325l14.1,19.956q2.714-1.95,5.224-4.153l-14.08-15.235A36.816,36.816,0,0,1,324.716,358.325Z" transform="translate(-224.174 -247.377)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_996" data-name="Path 996" d="M345.69,359.367c-.049,0-.094,0-.139,0l13.127,14.971q2.506-2.22,4.783-4.666l-12.233-10.728A36.956,36.956,0,0,1,345.69,359.367Z" transform="translate(-238.558 -247.802)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_997" data-name="Path 997" d="M366.956,354.966,378.468,365.6q2.262-2.464,4.281-5.148l-9.987-7.057A36.006,36.006,0,0,1,366.956,354.966Z" transform="translate(-253.336 -243.976)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_998" data-name="Path 998" d="M407.931,333.943l7.2,4.284c1.144-1.924,2.2-3.9,3.154-5.938l-5.2-2.232A36.4,36.4,0,0,1,407.931,333.943Z" transform="translate(-281.624 -227.862)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_999" data-name="Path 999" d="M28.9,293.885l-.964.191a67.851,67.851,0,0,0,1.879,6.551l2.631-.891A36.671,36.671,0,0,1,28.9,293.885Z" transform="translate(-19.289 -202.89)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1000" data-name="Path 1000" d="M309.1,74.638l15.64-20.944q-2.735-1.924-5.664-3.577l-14.6,26.071A35.707,35.707,0,0,1,309.1,74.638Z" transform="translate(-210.201 -34.599)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1001" data-name="Path 1001" d="M329.62,81.916l14.334-15.509c-1.685-1.466-3.453-2.846-5.279-4.142L324.268,82.651A35.77,35.77,0,0,1,329.62,81.916Z" transform="translate(-223.865 -42.986)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1002" data-name="Path 1002" d="M191.476,65.1l-1.494-7.5a11.084,11.084,0,0,0,1.535,1.089l1.959,8.673c.288.354.558.725.832,1.089l-.811-8.836a10.807,10.807,0,0,0,1.376.392L196.2,71.23l.17,1.411.014-1.127.146-11.279c.236.014.471.024.711.024.215,0,.423-.007.634-.017l.163,12.4,1.494-12.614a10.723,10.723,0,0,0,1.373-.381l-.946,10.323c.184-.277.374-.548.568-.818l2.35-10.406a11.2,11.2,0,0,0,1.529-1.057l-1.74,8.746q.723-.832,1.5-1.622l13.564-39.954q-3.172-.983-6.458-1.667L207.185,43.71a11.139,11.139,0,0,0-.659-1.085l4.413-19.5q-3.245-.655-6.586-1L202.786,39.2c-.242-.139-.492-.267-.745-.388l1.979-16.722q-3.25-.312-6.586-.316h-.063l.208,15.973c-.115,0-.225-.01-.34-.01-.139,0-.277.007-.416.01l.211-15.966c-2.243.014-4.461.138-6.642.36l1.972,16.7c-.25.121-.5.253-.742.4l-1.567-17.062c-2.225.239-4.416.589-6.572,1.037L187.9,42.708a11.19,11.19,0,0,0-.659,1.109l-4.083-20.528a66.337,66.337,0,0,0-6.43,1.7l13.051,38.449Q190.656,64.247,191.476,65.1Z" transform="translate(-122.006 -15.03)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1003" data-name="Path 1003" d="M290.668,69.959l16.067-27.007c-1.944-1.085-3.938-2.083-5.99-2.978L287.011,71.948A36.136,36.136,0,0,1,290.668,69.959Z" transform="translate(-198.144 -27.597)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1004" data-name="Path 1004" d="M346.559,91.555a36.2,36.2,0,0,1,4.7.333l12.378-10.857q-2.3-2.459-4.825-4.683L345.439,91.593C345.813,91.583,346.181,91.555,346.559,91.555Z" transform="translate(-238.481 -52.708)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1005" data-name="Path 1005" d="M274.815,67.815l15.419-33.444q-3.057-1.311-6.264-2.323L272.222,69.892C273.057,69.164,273.914,68.463,274.815,67.815Z" transform="translate(-187.934 -22.126)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1006" data-name="Path 1006" d="M173.2,69.15,161.822,32.507q-3.187,1.03-6.222,2.357L170.506,67.2Q171.9,68.114,173.2,69.15Z" transform="translate(-107.422 -22.442)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1007" data-name="Path 1007" d="M394.8,119.478l7.463-4.181q-1.763-2.9-3.789-5.6l-9.463,7.068A36.147,36.147,0,0,1,394.8,119.478Z" transform="translate(-268.562 -75.733)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1008" data-name="Path 1008" d="M373.382,104.432l9.973-7.047c-1.363-1.792-2.815-3.512-4.34-5.162l-11.623,10.746A35.8,35.8,0,0,1,373.382,104.432Z" transform="translate(-253.637 -63.668)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1009" data-name="Path 1009" d="M154.659,71.608,141.313,40.546c-2.035.9-4.028,1.9-5.955,2.988l15.613,26.237A36.558,36.558,0,0,1,154.659,71.608Z" transform="translate(-93.447 -27.992)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1010" data-name="Path 1010" d="M414.76,136.769l4.953-2.128q-1.456-3.078-3.2-5.976l-7.085,4.215A36.354,36.354,0,0,1,414.76,136.769Z" transform="translate(-282.66 -88.826)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1011" data-name="Path 1011" d="M273.274,334.927l11.377,36.643q3.182-1.035,6.226-2.357l-14.906-32.338Q274.572,335.965,273.274,334.927Z" transform="translate(-188.66 -231.224)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1012" data-name="Path 1012" d="M288.018,344.913l13.346,31.062q3.057-1.357,5.955-2.988L291.71,346.754A37.407,37.407,0,0,1,288.018,344.913Z" transform="translate(-198.839 -238.118)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1013" data-name="Path 1013" d="M324.716,358.325l14.1,19.956q2.714-1.95,5.224-4.153l-14.08-15.235A36.816,36.816,0,0,1,324.716,358.325Z" transform="translate(-224.174 -247.377)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1014" data-name="Path 1014" d="M131.43,353.011l-15.64,20.947q2.735,1.919,5.664,3.574l14.6-26.071A36.053,36.053,0,0,1,131.43,353.011Z" transform="translate(-79.938 -242.639)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1015" data-name="Path 1015" d="M151.079,344.53l-16.067,27.007c1.941,1.085,3.934,2.083,5.986,2.977l13.737-31.974A36.187,36.187,0,0,1,151.079,344.53Z" transform="translate(-93.208 -236.48)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1016" data-name="Path 1016" d="M245.515,299.706l-.017,1.13c.059.1.125.19.184.284Z" transform="translate(-169.485 -206.908)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1017" data-name="Path 1017" d="M345.69,359.367c-.049,0-.094,0-.139,0l13.127,14.971q2.506-2.22,4.783-4.666l-12.233-10.728A36.956,36.956,0,0,1,345.69,359.367Z" transform="translate(-238.558 -247.802)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1018" data-name="Path 1018" d="M112.2,358.418,97.865,373.927q2.532,2.2,5.283,4.142l14.4-20.382A36.208,36.208,0,0,1,112.2,358.418Z" transform="translate(-67.563 -246.937)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1019" data-name="Path 1019" d="M170.746,333.6,155.331,367.04c2.035.877,4.129,1.647,6.26,2.323l11.748-37.839C172.507,332.251,171.648,332.948,170.746,333.6Z" transform="translate(-107.236 -228.874)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1020" data-name="Path 1020" d="M425.765,317.97l4.967,2.288a66.641,66.641,0,0,0,2.531-6.243l-3.092-.964A36.421,36.421,0,0,1,425.765,317.97Z" transform="translate(-293.936 -216.121)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1021" data-name="Path 1021" d="M366.956,354.966,378.468,365.6q2.262-2.464,4.281-5.148l-9.987-7.057A36.006,36.006,0,0,1,366.956,354.966Z" transform="translate(-253.336 -243.976)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1022" data-name="Path 1022" d="M388.037,346.4l9.456,7.061q2-2.683,3.733-5.571l-7.564-4.236A35.937,35.937,0,0,1,388.037,346.4Z" transform="translate(-267.889 -237.252)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1023" data-name="Path 1023" d="M407.931,333.943l7.2,4.284c1.144-1.924,2.2-3.9,3.154-5.938l-5.2-2.232A36.4,36.4,0,0,1,407.931,333.943Z" transform="translate(-281.624 -227.862)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1024" data-name="Path 1024" d="M305.393,353.085,319.6,378.459c1.931-1.109,3.8-2.316,5.609-3.6l-15.256-20.431A35.8,35.8,0,0,1,305.393,353.085Z" transform="translate(-210.834 -243.759)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1025" data-name="Path 1025" d="M98.581,359.583a36.906,36.906,0,0,1-4.7-.329L81.5,370.107q2.3,2.459,4.829,4.687L99.7,359.545C99.322,359.556,98.958,359.583,98.581,359.583Z" transform="translate(-56.264 -248.018)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1026" data-name="Path 1026" d="M34.208,171.593l-2.95-1a66.839,66.839,0,0,0-1.851,6.492l1.373.274A35.807,35.807,0,0,1,34.208,171.593Z" transform="translate(-20.302 -117.771)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1027" data-name="Path 1027" d="M24.811,215.1c-.069.572-.149,1.144-.208,1.726.111-.579.236-1.144.371-1.705Z" transform="translate(-16.985 -148.501)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1028" data-name="Path 1028" d="M24.6,277.517c.26,1.678.592,3.331.967,4.964l.895-.2A36.457,36.457,0,0,1,24.6,277.517Z" transform="translate(-16.986 -191.59)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1029" data-name="Path 1029" d="M43.23,151.742l-4.967-2.288c-.936,2.031-1.789,4.111-2.53,6.246l3.092.957A36.834,36.834,0,0,1,43.23,151.742Z" transform="translate(-24.668 -103.179)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1030" data-name="Path 1030" d="M28.9,293.885l-.964.191a67.851,67.851,0,0,0,1.879,6.551l2.631-.891A36.671,36.671,0,0,1,28.9,293.885Z" transform="translate(-19.289 -202.89)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1031" data-name="Path 1031" d="M136.136,76.126,121.927,50.755q-2.9,1.659-5.612,3.595l15.259,20.434A36.135,36.135,0,0,1,136.136,76.126Z" transform="translate(-80.3 -35.04)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1032" data-name="Path 1032" d="M117.907,82.963,103.8,63q-2.714,1.95-5.22,4.153l14.08,15.235A35.779,35.779,0,0,1,117.907,82.963Z" transform="translate(-68.058 -43.495)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1033" data-name="Path 1033" d="M37.125,313.812l-2.77.863a66.711,66.711,0,0,0,2.569,6.288l4.735-2.184A36.266,36.266,0,0,1,37.125,313.812Z" transform="translate(-23.718 -216.647)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1034" data-name="Path 1034" d="M54.71,133.62l-7.2-4.284q-1.716,2.886-3.151,5.938l5.2,2.236A36.556,36.556,0,0,1,54.71,133.62Z" transform="translate(-30.627 -89.29)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1035" data-name="Path 1035" d="M100.1,92.089c.049,0,.1.007.142.007L87.11,77.121q-2.506,2.22-4.783,4.669L94.56,92.515A36.913,36.913,0,0,1,100.1,92.089Z" transform="translate(-56.836 -53.242)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1036" data-name="Path 1036" d="M83.554,103.6,72.042,92.962q-2.267,2.465-4.281,5.148l9.983,7.058A36.006,36.006,0,0,1,83.554,103.6Z" transform="translate(-46.78 -64.178)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1037" data-name="Path 1037" d="M68.271,117.51l-9.46-7.061q-2,2.683-3.733,5.571l7.567,4.236A35.609,35.609,0,0,1,68.271,117.51Z" transform="translate(-38.024 -76.251)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1038" data-name="Path 1038" d="M27.855,192.858l-1.293-.291q-.749,3.27-1.175,6.666l.208.021A35.878,35.878,0,0,1,27.855,192.858Z" transform="translate(-17.526 -132.943)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1039" data-name="Path 1039" d="M48.037,330.941l-4.95,2.128a66.643,66.643,0,0,0,3.193,5.976l7.089-4.215A36.384,36.384,0,0,1,48.037,330.941Z" transform="translate(-29.746 -228.472)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1040" data-name="Path 1040" d="M61.364,344.5,53.9,348.676q1.763,2.9,3.789,5.6l9.463-7.068A36.12,36.12,0,0,1,61.364,344.5Z" transform="translate(-37.212 -237.832)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1041" data-name="Path 1041" d="M76.749,354.127l-9.973,7.051c1.359,1.792,2.811,3.508,4.34,5.155l11.619-10.739A36.14,36.14,0,0,1,76.749,354.127Z" transform="translate(-46.1 -244.479)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1042" data-name="Path 1042" d="M84.726,13.8a70.924,70.924,0,1,0,70.919,70.926A70.927,70.927,0,0,0,84.726,13.8Z" transform="translate(-9.527 -9.527)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1043" data-name="Path 1043" d="M456.966,197.195a36.165,36.165,0,0,0-1.858-4.759l.895-.2C456.377,193.867,456.71,195.517,456.966,197.195Z" transform="translate(-314.193 -132.711)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1044" data-name="Path 1044" d="M447.718,176.671l-.964.191a36.263,36.263,0,0,0-3.546-5.848l2.628-.894A66.948,66.948,0,0,1,447.718,176.671Z" transform="translate(-305.978 -117.446)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1045" data-name="Path 1045" d="M435.076,155.163l-2.77.86a36.489,36.489,0,0,0-4.538-4.967l4.735-2.184A66.449,66.449,0,0,1,435.076,155.163Z" transform="translate(-295.319 -102.776)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1046" data-name="Path 1046" d="M419.713,134.64l-4.953,2.128a36.354,36.354,0,0,0-5.328-3.889l7.085-4.215Q418.259,131.56,419.713,134.64Z" transform="translate(-282.66 -88.826)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1047" data-name="Path 1047" d="M402.263,115.3l-7.463,4.181a36.123,36.123,0,0,0-5.789-2.711l9.463-7.068Q400.5,112.4,402.263,115.3Z" transform="translate(-268.562 -75.734)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1048" data-name="Path 1048" d="M379.016,92.223c1.525,1.65,2.978,3.369,4.34,5.162l-9.973,7.047a35.841,35.841,0,0,0-5.99-1.463Z" transform="translate(-253.637 -63.668)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1049" data-name="Path 1049" d="M363.637,81.031,351.259,91.888a36.2,36.2,0,0,0-4.7-.333c-.378,0-.745.028-1.12.038l13.373-15.245Q361.339,78.569,363.637,81.031Z" transform="translate(-238.481 -52.708)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1050" data-name="Path 1050" d="M343.955,66.407,329.621,81.916a35.772,35.772,0,0,0-5.352.735l14.406-20.386C340.5,63.561,342.27,64.941,343.955,66.407Z" transform="translate(-223.866 -42.986)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1051" data-name="Path 1051" d="M324.74,53.694,309.1,74.638a35.676,35.676,0,0,0-4.624,1.549l14.6-26.071Q322,51.771,324.74,53.694Z" transform="translate(-210.201 -34.599)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1052" data-name="Path 1052" d="M306.735,42.953,290.668,69.96a36.074,36.074,0,0,0-3.657,1.99l13.734-31.974C302.8,40.87,304.79,41.868,306.735,42.953Z" transform="translate(-198.144 -27.598)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1053" data-name="Path 1053" d="M290.232,34.371,274.814,67.814c-.9.648-1.757,1.348-2.593,2.076l11.748-37.843Q287.171,33.067,290.232,34.371Z" transform="translate(-187.933 -22.125)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1054" data-name="Path 1054" d="M183.156,23.289l4.083,20.528a11.128,11.128,0,0,1,.659-1.109l-4.413-19.495c2.156-.447,4.347-.8,6.572-1.037l1.567,17.062c.243-.142.492-.274.742-.4l-1.972-16.7c2.18-.222,4.4-.346,6.642-.36l-.211,15.966c.139,0,.277-.011.416-.011.114,0,.225.007.34.011l-.208-15.973h.063q3.338,0,6.586.316l-1.979,16.722c.253.121.5.25.745.388l1.567-17.072q3.344.343,6.586,1l-4.413,19.5a11.166,11.166,0,0,1,.659,1.085l4.083-20.521q3.286.681,6.458,1.667L204.163,64.81q-.775.79-1.5,1.622l1.74-8.746a11.183,11.183,0,0,1-1.529,1.057l-2.35,10.406c-.194.27-.385.541-.568.818l.946-10.323a10.724,10.724,0,0,1-1.373.381l-1.494,12.614-.163-12.4c-.211.011-.419.017-.634.017-.239,0-.475-.01-.711-.024l-.146,11.279-.014,1.127-.17-1.411-1.328-11.221a10.807,10.807,0,0,1-1.376-.392l.811,8.836c-.274-.364-.544-.735-.832-1.088l-1.958-8.673a11.084,11.084,0,0,1-1.535-1.089l1.494,7.5q-.822-.858-1.7-1.664L176.726,24.991A66.337,66.337,0,0,1,183.156,23.289Z" transform="translate(-122.006 -15.03)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1055" data-name="Path 1055" d="M161.821,32.507,173.2,69.15q-1.3-1.035-2.693-1.952L155.6,34.864Q158.63,33.538,161.821,32.507Z" transform="translate(-107.421 -22.442)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1056" data-name="Path 1056" d="M141.313,40.546l13.346,31.062a36.492,36.492,0,0,0-3.688-1.837L135.358,43.534C137.285,42.442,139.279,41.447,141.313,40.546Z" transform="translate(-93.447 -27.992)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1057" data-name="Path 1057" d="M121.928,50.756l14.209,25.371a36.161,36.161,0,0,0-4.562-1.342L116.316,54.351Q119.025,52.421,121.928,50.756Z" transform="translate(-80.301 -35.04)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1058" data-name="Path 1058" d="M103.8,63l14.1,19.96a35.821,35.821,0,0,0-5.245-.572L98.582,67.157Q101.083,64.957,103.8,63Z" transform="translate(-68.058 -43.496)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1059" data-name="Path 1059" d="M87.11,77.121,100.237,92.1c-.045,0-.094-.007-.142-.007a36.91,36.91,0,0,0-5.536.426L82.326,81.79Q84.6,79.336,87.11,77.121Z" transform="translate(-56.835 -53.242)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1060" data-name="Path 1060" d="M72.043,92.962,83.555,103.6a36.018,36.018,0,0,0-5.81,1.563L67.762,98.11Q69.774,95.432,72.043,92.962Z" transform="translate(-46.781 -64.178)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1061" data-name="Path 1061" d="M58.81,110.449l9.46,7.061a35.626,35.626,0,0,0-5.626,2.745l-7.567-4.236Q56.808,113.139,58.81,110.449Z" transform="translate(-38.024 -76.251)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1062" data-name="Path 1062" d="M47.514,129.336l7.2,4.284a36.56,36.56,0,0,0-5.151,3.89l-5.2-2.236Q45.793,132.217,47.514,129.336Z" transform="translate(-30.627 -89.29)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1063" data-name="Path 1063" d="M38.262,149.454l4.967,2.288a36.814,36.814,0,0,0-4.406,4.915l-3.092-.957C36.473,153.565,37.326,151.485,38.262,149.454Z" transform="translate(-24.668 -103.179)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1064" data-name="Path 1064" d="M31.258,170.591l2.95,1a35.821,35.821,0,0,0-3.428,5.765l-1.373-.274A66.931,66.931,0,0,1,31.258,170.591Z" transform="translate(-20.302 -117.771)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1065" data-name="Path 1065" d="M26.562,192.567l1.293.291a35.906,35.906,0,0,0-2.26,6.4l-.208-.021Q25.818,195.843,26.562,192.567Z" transform="translate(-17.526 -132.943)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1066" data-name="Path 1066" d="M24.811,215.1l.163.021c-.135.562-.26,1.127-.371,1.705C24.662,216.247,24.742,215.675,24.811,215.1Z" transform="translate(-16.985 -148.501)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1067" data-name="Path 1067" d="M23.047,271.292a69.307,69.307,0,0,0,2.343,6.69l-.894.2C24.122,276.55,23.3,272.97,23.047,271.292Z" transform="translate(-15.911 -187.292)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1068" data-name="Path 1068" d="M27.94,294.075l.964-.191a36.7,36.7,0,0,0,3.546,5.851l-2.631.891A67.806,67.806,0,0,1,27.94,294.075Z" transform="translate(-19.289 -202.889)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1069" data-name="Path 1069" d="M34.355,314.675l2.77-.863a36.249,36.249,0,0,0,4.534,4.967l-4.735,2.184A66.711,66.711,0,0,1,34.355,314.675Z" transform="translate(-23.718 -216.647)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1070" data-name="Path 1070" d="M43.087,333.069l4.95-2.128a36.384,36.384,0,0,0,5.331,3.889l-7.089,4.215A66.565,66.565,0,0,1,43.087,333.069Z" transform="translate(-29.746 -228.472)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1071" data-name="Path 1071" d="M53.9,348.676l7.463-4.177a36.106,36.106,0,0,0,5.789,2.711l-9.464,7.068Q55.663,351.579,53.9,348.676Z" transform="translate(-37.212 -237.832)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1072" data-name="Path 1072" d="M71.116,366.331c-1.529-1.647-2.981-3.362-4.34-5.154l9.973-7.051a36.108,36.108,0,0,0,5.987,1.466Z" transform="translate(-46.1 -244.478)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1073" data-name="Path 1073" d="M81.5,370.107l12.378-10.853a36.91,36.91,0,0,0,4.7.329c.378,0,.742-.028,1.116-.038l-13.37,15.249Q83.795,372.574,81.5,370.107Z" transform="translate(-56.264 -248.018)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1074" data-name="Path 1074" d="M97.866,373.927,112.2,358.418a36.209,36.209,0,0,0,5.352-.731l-14.4,20.382Q100.4,376.12,97.866,373.927Z" transform="translate(-67.564 -246.937)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1075" data-name="Path 1075" d="M115.789,373.959l15.64-20.947a36.034,36.034,0,0,0,4.624-1.55l-14.6,26.071Q118.526,375.885,115.789,373.959Z" transform="translate(-79.937 -242.639)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1076" data-name="Path 1076" d="M135.012,371.535l16.067-27.007a36.078,36.078,0,0,0,3.657-1.99L141,374.513C138.946,373.619,136.953,372.62,135.012,371.535Z" transform="translate(-93.208 -236.479)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1077" data-name="Path 1077" d="M155.331,367.04,170.746,333.6c.9-.648,1.761-1.345,2.593-2.073l-11.748,37.839C159.46,368.686,157.366,367.917,155.331,367.04Z" transform="translate(-107.236 -228.874)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1078" data-name="Path 1078" d="M176.58,361.336l13.564-39.954q.77-.785,1.5-1.626L183.038,363Q179.746,362.324,176.58,361.336Z" transform="translate(-121.906 -220.751)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1079" data-name="Path 1079" d="M219.374,350.173l-2.312-25.163a6.483,6.483,0,0,1-.9.863l2.88,24.337q-3.276.338-6.642.36l.3-23.329c-.18.014-.364.028-.551.028-.132,0-.263-.011-.4-.021l.3,23.329c-.024,0-.045,0-.066,0-2.222,0-4.416-.114-6.583-.322l2.874-24.282a6.6,6.6,0,0,1-.9-.818l-2.305,25.062c-2.229-.222-4.423-.562-6.586-.995L208.906,303.2c.191-.27.381-.541.565-.818l-1.217,13.255a6.391,6.391,0,0,1,1.348-.79l1.792-15.142.191,14.646c.187-.014.378-.028.565-.028a6.175,6.175,0,0,1,.721.045l.173-13.533.017-1.13.167,1.414,1.633,13.807a6.348,6.348,0,0,1,1.352.832L215.121,303.9c.274.364.544.728.832,1.085l9.99,44.152Q222.709,349.808,219.374,350.173Z" transform="translate(-137.031 -206.908)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1080" data-name="Path 1080" d="M269.678,365.875l-8.316-41.819c.548.575,1.109,1.13,1.7,1.664l13.051,38.446A66.346,66.346,0,0,1,269.678,365.875Z" transform="translate(-180.437 -223.719)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1081" data-name="Path 1081" d="M284.651,371.569l-11.377-36.643q1.3,1.035,2.7,1.948l14.906,32.338Q287.84,370.538,284.651,371.569Z" transform="translate(-188.66 -231.223)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1082" data-name="Path 1082" d="M301.364,375.975l-13.346-31.062a37.33,37.33,0,0,0,3.692,1.84l15.609,26.234Q304.428,374.625,301.364,375.975Z" transform="translate(-198.839 -238.118)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1083" data-name="Path 1083" d="M319.6,378.459l-14.209-25.374a35.814,35.814,0,0,0,4.562,1.342l15.256,20.431C323.4,376.144,321.533,377.35,319.6,378.459Z" transform="translate(-210.834 -243.759)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1084" data-name="Path 1084" d="M338.821,378.281l-14.1-19.956a36.865,36.865,0,0,0,5.248.568l14.08,15.235Q341.538,376.328,338.821,378.281Z" transform="translate(-224.174 -247.377)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1085" data-name="Path 1085" d="M358.678,374.335l-13.127-14.971c.045,0,.09,0,.139,0a36.956,36.956,0,0,0,5.539-.426l12.233,10.728Q361.19,372.118,358.678,374.335Z" transform="translate(-238.558 -247.802)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1086" data-name="Path 1086" d="M378.468,365.6l-11.512-10.638a36.021,36.021,0,0,0,5.807-1.567l9.986,7.058Q380.731,363.135,378.468,365.6Z" transform="translate(-253.336 -243.976)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1087" data-name="Path 1087" d="M397.493,353.466l-9.456-7.061a35.908,35.908,0,0,0,5.626-2.745l7.564,4.236Q399.5,350.776,397.493,353.466Z" transform="translate(-267.889 -237.252)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1088" data-name="Path 1088" d="M415.128,338.227l-7.2-4.284a36.421,36.421,0,0,0,5.151-3.886l5.2,2.232C417.329,334.324,416.272,336.3,415.128,338.227Z" transform="translate(-281.624 -227.862)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1089" data-name="Path 1089" d="M430.732,320.258l-4.967-2.288a36.4,36.4,0,0,0,4.406-4.919l3.092.964A66.607,66.607,0,0,1,430.732,320.258Z" transform="translate(-293.936 -216.121)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1090" data-name="Path 1090" d="M443.758,300.095l-2.946-1a36.128,36.128,0,0,0,3.428-5.771l1.373.274C445.1,295.807,444.483,297.977,443.758,300.095Z" transform="translate(-304.324 -202.503)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1091" data-name="Path 1091" d="M453.655,278.3l-1.29-.291a35.987,35.987,0,0,0,2.257-6.4l.208.021Q454.4,275.023,453.655,278.3Z" transform="translate(-312.299 -187.516)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1092" data-name="Path 1092" d="M460.074,266.836l-.163-.017c.139-.565.26-1.134.371-1.705C460.227,265.692,460.147,266.264,460.074,266.836Z" transform="translate(-317.509 -183.026)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1093" data-name="Path 1093" d="M236.173,299.706l-1.792,15.141a6.318,6.318,0,0,1,1.983-.5Z" transform="translate(-161.81 -206.908)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1094" data-name="Path 1094" d="M220.846,408.838c2.167.208,4.361.322,6.583.322.021,0,.041,0,.066,0l-.305-23.329a6.431,6.431,0,0,1-3.47-1.272Z" transform="translate(-152.466 -265.486)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1095" data-name="Path 1095" d="M208.254,321.6l1.217-13.256c-.184.277-.375.548-.565.818l-10.417,46.027c2.163.433,4.357.773,6.586.995l2.305-25.062a6.45,6.45,0,0,1,.873-9.522Z" transform="translate(-137.031 -212.875)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1096" data-name="Path 1096" d="M190.144,321.383,176.58,361.336q3.167.988,6.458,1.664l8.6-43.243Q190.914,320.6,190.144,321.383Z" transform="translate(-121.906 -220.751)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1097" data-name="Path 1097" d="M261.362,324.056l8.316,41.819a66.346,66.346,0,0,0,6.43-1.709L263.057,325.72C262.471,325.186,261.91,324.631,261.362,324.056Z" transform="translate(-180.437 -223.719)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1098" data-name="Path 1098" d="M252.2,313.252l1.092,11.858a6.464,6.464,0,0,1,.849,9.252l2.312,25.163q3.338-.364,6.569-1.036l-9.99-44.152C252.748,313.98,252.478,313.616,252.2,313.252Z" transform="translate(-174.114 -216.26)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1099" data-name="Path 1099" d="M245.3,303.64c-.059-.094-.125-.187-.184-.284l-.173,13.533a6.312,6.312,0,0,1,1.99.558Z" transform="translate(-169.099 -209.428)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1100" data-name="Path 1100" d="M243.4,408.917q3.364-.021,6.642-.36l-2.88-24.338a6.433,6.433,0,0,1-3.456,1.369Z" transform="translate(-168.04 -265.254)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1101" data-name="Path 1101" d="M234.723,355.52l.78,1.009a1.538,1.538,0,0,0,.187.211.416.416,0,0,0,.156.062v.059h-1.03V356.8c.076-.017.115-.052.115-.1a.243.243,0,0,0-.069-.129l-.482-.627-.489.607a.281.281,0,0,0-.076.142c0,.063.041.1.128.107v.059h-.995V356.8a.268.268,0,0,0,.135-.052,1.861,1.861,0,0,0,.194-.222l.79-.988-.728-.939a1.722,1.722,0,0,0-.187-.222.261.261,0,0,0-.149-.045v-.062h1.037v.062c-.076.021-.115.059-.115.115a.349.349,0,0,0,.08.152l.405.516.416-.516a.407.407,0,0,0,.093-.163c0-.059-.045-.09-.135-.1v-.062H235.8v.062a.213.213,0,0,0-.128.042,1.891,1.891,0,0,0-.2.225Z" transform="translate(-160.82 -244.579)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1102" data-name="Path 1102" d="M244.161,355.52l.783,1.009a1.944,1.944,0,0,0,.184.211.4.4,0,0,0,.153.062v.059h-1.023V356.8c.073-.017.111-.052.111-.1a.294.294,0,0,0-.066-.129l-.485-.627-.489.607a.313.313,0,0,0-.076.142c0,.063.041.1.128.107v.059h-.995V356.8a.282.282,0,0,0,.135-.052,1.865,1.865,0,0,0,.194-.222l.79-.988-.728-.939a1.722,1.722,0,0,0-.187-.222.261.261,0,0,0-.149-.045l0-.062h1.04v.062c-.073.021-.111.059-.111.115a.314.314,0,0,0,.08.152l.4.516.42-.516a.384.384,0,0,0,.09-.163c0-.059-.042-.09-.132-.1v-.062h1.012v.062a.213.213,0,0,0-.128.042,2.123,2.123,0,0,0-.2.225Z" transform="translate(-167.336 -244.579)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1103" data-name="Path 1103" d="M234.723,373.8l.78,1.009a1.536,1.536,0,0,0,.187.211.366.366,0,0,0,.156.059v.063h-1.03v-.062c.076-.014.115-.049.115-.1a.243.243,0,0,0-.069-.129l-.482-.627-.489.607a.281.281,0,0,0-.076.142c0,.063.041.1.128.1v.063h-.995v-.062a.24.24,0,0,0,.135-.049,1.853,1.853,0,0,0,.194-.222l.79-.988-.728-.939a1.722,1.722,0,0,0-.187-.222.261.261,0,0,0-.149-.045v-.059h1.037v.059c-.076.021-.115.059-.115.115a.349.349,0,0,0,.08.152l.405.516.416-.516a.407.407,0,0,0,.093-.163c0-.059-.045-.09-.135-.1v-.059H235.8v.059a.213.213,0,0,0-.128.042,1.893,1.893,0,0,0-.2.225Z" transform="translate(-160.82 -257.2)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1104" data-name="Path 1104" d="M244.161,373.8l.783,1.009a1.94,1.94,0,0,0,.184.211.349.349,0,0,0,.153.059v.063h-1.023v-.062c.073-.014.111-.049.111-.1a.294.294,0,0,0-.066-.129l-.485-.627-.489.607a.313.313,0,0,0-.076.142c0,.063.041.1.128.1v.063h-.995v-.062a.25.25,0,0,0,.135-.049,1.857,1.857,0,0,0,.194-.222l.79-.988-.728-.939a1.722,1.722,0,0,0-.187-.222.261.261,0,0,0-.149-.045l0-.059h1.04v.059c-.073.021-.111.059-.111.115a.314.314,0,0,0,.08.152l.4.516.42-.516a.383.383,0,0,0,.09-.163c0-.059-.042-.09-.132-.1v-.059h1.012v.059a.213.213,0,0,0-.128.042,2.126,2.126,0,0,0-.2.225Z" transform="translate(-167.336 -257.2)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1105" data-name="Path 1105" d="M237.09,367.483h-9.023v-.527h9.023l.423.26-.423.267" transform="translate(-157.451 -253.336)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1106" data-name="Path 1106" d="M222.568,111.35a27.37,27.37,0,0,1-5-1.4,26.215,26.215,0,0,0,5.151,1.785" transform="translate(-150.201 -75.903)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1107" data-name="Path 1107" d="M235.015,111.763c1.307,0,4.551-1.355,5.546-1.355,1.22,0,4.676.232,4.676.232a45.785,45.785,0,0,0-5.362-.582c-.829,0-3.7,1.161-5.023,1.331" transform="translate(-162.135 -75.981)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1108" data-name="Path 1108" d="M241.362,96.913a.584.584,0,0,0,.3-.083h.049a.1.1,0,0,0,.138-.052l.18-.416a.107.107,0,0,0-.055-.139l-.038-.028a.588.588,0,0,0-1.151,0l-.035.028a.105.105,0,0,0-.052.139l.181.416a.1.1,0,0,0,.135.052h.042A.648.648,0,0,0,241.362,96.913Z" transform="translate(-166.163 -66.087)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_35" data-name="Line 35" y1="0.728" transform="translate(74.991 30.791)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_36" data-name="Line 36" x1="0.003" y1="0.728" transform="translate(75.417 30.791)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1109" data-name="Path 1109" d="M242.733,114.343a2.688,2.688,0,0,0,2.312-1.671l.18-.5a4.359,4.359,0,0,0,.09-.634" transform="translate(-167.576 -77)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_37" data-name="Line 37" y1="1.075" transform="translate(77.747 33.085)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1110" data-name="Path 1110" d="M239.565,104.5v-.177a3.777,3.777,0,0,1-.967-.281,1.13,1.13,0,0,1-.607-.714h-2.028a1.129,1.129,0,0,1-.607.714,3.77,3.77,0,0,1-.964.281v1.491l0-.035a2.866,2.866,0,0,0,2.579,2.9" transform="translate(-161.818 -71.335)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1111" data-name="Path 1111" d="M242.6,114.57a3.1,3.1,0,0,0,2.749-2.122l.128-.433a4.246,4.246,0,0,0,.087-.7" transform="translate(-167.483 -76.846)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_38" data-name="Line 38" y1="0.936" transform="translate(78.086 33.12)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1112" data-name="Path 1112" d="M239.146,103.558v-.6a4.4,4.4,0,0,1-1.1-.277.716.716,0,0,1-.44-.718h-2.776a.709.709,0,0,1-.44.718,4.39,4.39,0,0,1-1.1.277v1.491l0,.333a3.25,3.25,0,0,0,2.971,3.338" transform="translate(-161.06 -70.392)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1113" data-name="Path 1113" d="M252.227,102.087s1.092-1.126,1.893-1.893" transform="translate(-174.13 -69.171)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1114" data-name="Path 1114" d="M255.209,93.883a.861.861,0,0,1,.173-.139c.163.069.357-.111.364-.291a.33.33,0,0,0-.659-.035c0,.038-3.477,3.4-3.477,3.4" transform="translate(-173.705 -64.278)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1115" data-name="Path 1115" d="M257.6,96.157l1.9,1.9.378-1.064,1.068-.368-1.875-1.917" transform="translate(-177.84 -65.384)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1116" data-name="Path 1116" d="M229.023,102.087s-1.092-1.126-1.9-1.893" transform="translate(-156.802 -69.171)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1117" data-name="Path 1117" d="M221.043,93.883a.681.681,0,0,0-.173-.139c-.163.069-.353-.111-.36-.291a.328.328,0,1,1,.655-.035c0,.038,3.477,3.4,3.477,3.4" transform="translate(-152.232 -64.278)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1118" data-name="Path 1118" d="M220.426,96.157l-1.9,1.9-.374-1.064-1.068-.368,1.872-1.917" transform="translate(-149.869 -65.384)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1119" data-name="Path 1119" d="M233.569,125.049h-.01a.278.278,0,0,1-.277-.277.272.272,0,0,1,.156-.246v-.167c-.472-.111-2.822-.385-3.758-2.44l-.007,0a.275.275,0,0,1-.274-.277.272.272,0,0,1,.274-.274.275.275,0,0,1,.277.274.265.265,0,0,1-.062.173l.035.021a4.523,4.523,0,0,0,3.418,2.024l0-2.316" transform="translate(-158.37 -83.786)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1120" data-name="Path 1120" d="M242.834,125.049h.011a.276.276,0,0,0,.121-.523v-.167c.472-.111,2.825-.385,3.761-2.44l0,0a.274.274,0,0,0,.27-.277.272.272,0,1,0-.544,0,.264.264,0,0,0,.063.173l-.038.021a4.519,4.519,0,0,1-3.414,2.024l-.007-2.316" transform="translate(-167.645 -83.786)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1121" data-name="Path 1121" d="M243.8,176.656a33.84,33.84,0,0,0,32.414,24.133c18.681,0,33.822-18.344,33.822-33.825a33.827,33.827,0,0,0-66.2-9.789" transform="translate(-168.31 -91.917)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1122" data-name="Path 1122" d="M96.194,156.847a33.827,33.827,0,1,0-.063,19.5" transform="translate(-20.672 -91.593)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1123" data-name="Path 1123" d="M361.315,248.989l-1.893-.468-.683,2.028" transform="translate(-247.663 -171.572)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1124" data-name="Path 1124" d="M360.479,242.35l-1.906-.031-.326,1.92-1.21.447" transform="translate(-246.488 -167.29)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_39" data-name="Line 39" x2="0.873" y2="1.854" transform="translate(113.617 72.623)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_40" data-name="Line 40" x2="0.423" y2="1.078" transform="translate(115.7 73.005)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1125" data-name="Path 1125" d="M369.513,233.452l.35.721-1.709,2.055.381,1.751" transform="translate(-254.163 -161.169)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_41" data-name="Line 41" y1="0.905" x2="1.126" transform="translate(113.991 65.531)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1126" data-name="Path 1126" d="M362.108,204.151l-2.347-.42-1.023-1.816" transform="translate(-247.663 -139.396)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_42" data-name="Line 42" x1="1.785" y2="0.097" transform="translate(110.178 64.789)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_43" data-name="Line 43" x1="1.601" y1="0.766" transform="translate(112.549 66.935)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1127" data-name="Path 1127" d="M362.7,209.882l-2.447-.492.5-1.608" transform="translate(-248.706 -143.446)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_44" data-name="Line 44" y1="1.861" x2="0.873" transform="translate(113.617 67.015)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_45" data-name="Line 45" x1="0.527" y1="0.052" transform="translate(115.991 68.46)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1128" data-name="Path 1128" d="M370.127,217.7l.028-1.106-2-2.024" transform="translate(-254.163 -148.13)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_46" data-name="Line 46" x1="3.12" transform="translate(113.652 72.318)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_47" data-name="Line 47" x1="2.964" y2="0.003" transform="translate(113.808 69.628)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1129" data-name="Path 1129" d="M336.991,211.174l.329.1.336-.059.315.139.343-.014.291.181.34.024.27.215.333.073.243.243.322.111.208.274.3.156.17.3.288.187.132.312.26.229.094.329.225.253.052.34.2.281.007.343.156.3-.035.343.118.319-.077.333.077.336-.118.319.035.343-.156.3-.007.343-.2.281-.052.34-.225.253-.094.333-.26.222-.132.315-.288.187-.17.3-.3.156-.208.27-.322.111-.243.246-.333.069-.27.215-.34.028-.291.18-.343-.014-.315.139-.336-.059-.329.1-.329-.1-.336.059-.312-.139-.343.014-.291-.18-.343-.028-.267-.215-.336-.069-.239-.246-.322-.111-.208-.27-.309-.156-.17-.3-.284-.187-.132-.315-.26-.222-.09-.333-.232-.253-.052-.34-.194-.281-.007-.343-.159-.3.038-.343-.121-.319.08-.336-.08-.333.121-.319-.038-.343.159-.3.007-.343.194-.281.052-.34.232-.253.09-.329.26-.229.132-.312.284-.187.17-.3.309-.156.208-.274.322-.111.239-.243.336-.073.267-.215.343-.024.291-.181.343.014.312-.139.336.059.329-.1" transform="translate(-228.989 -145.788)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1130" data-name="Path 1130" d="M335.677,209.282l.364.107.374-.066.35.156.381-.014.322.194.381.035.3.232.371.08.27.27.357.128.228.3.343.17.19.329.315.208.146.35.291.25.1.368.253.281.059.378.218.316.007.378.173.34-.038.378.135.354-.09.371.09.371-.135.357.038.378-.173.34-.007.381-.218.312-.059.374-.253.284-.1.364-.291.25-.146.35-.315.211-.19.329-.343.17-.228.3-.357.125-.27.271-.371.08-.3.236-.381.035-.322.194-.381-.014-.35.156-.374-.066-.364.107-.364-.107-.375.066-.346-.156-.385.014-.322-.194-.378-.035-.3-.236-.374-.08-.267-.271-.36-.125-.229-.3-.34-.17-.19-.329-.316-.211-.149-.35-.288-.25-.1-.364-.26-.284-.055-.374-.215-.312-.007-.381-.18-.34.045-.378-.135-.357.09-.371-.09-.371.135-.354-.045-.378.18-.34.007-.378.215-.316.055-.378.26-.281.1-.368.288-.25.149-.35.316-.208.19-.329.34-.17.229-.3.36-.128.267-.27.374-.08.3-.232.378-.035.322-.194.385.014.346-.156.375.066.364-.107" transform="translate(-227.676 -144.482)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1131" data-name="Path 1131" d="M327.632,217.2l-.364-.17-.579.024-.492-.3-.572-.049-.454-.357-.561-.121-.406-.409-.544-.191-.35-.461-.513-.253-.288-.5-.485-.322-.218-.53-.437-.378-.159-.551-.381-.433-.087-.565-.326-.478-.017-.575-.263-.51.055-.575-.2-.541.132-.561-.132-.558.2-.541-.055-.576.263-.509.017-.579.326-.472.087-.572.381-.43.159-.555.437-.374.218-.53.485-.322.288-.5.513-.253.35-.461.544-.19.406-.409.561-.121.454-.361.572-.048.492-.3.579.024.527-.232.572.094.548-.166.555.166.565-.094.527.232.575-.024.5.3.572.048.454.361.565.121.4.409.544.19.35.461.516.253.288.5.482.322.225.53.437.374.149.555.388.43.083.572.329.472.014.579.267.509-.062.576.2.541-.132.558.132.561-.2.541.062.575-.267.51-.014.575-.329.478-.083.565-.388.433-.149.551-.437.378-.225.53-.482.322-.288.5-.516.253-.35.461-.544.191-.4.409-.565.121-.454.357-.572.049-.5.3-.575-.024-.43.191" transform="translate(-220.913 -137.696)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1132" data-name="Path 1132" d="M355.1,245.492l.312,3.085.239-.163.007-3.207" transform="translate(-245.151 -169.284)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_48" data-name="Line 48" x1="1.21" y2="2.659" transform="translate(109.53 62.307)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_49" data-name="Line 49" y1="2.798" x2="1.012" transform="translate(110.102 62.377)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1133" data-name="Path 1133" d="M341.09,245.492,340.8,248.6l-.26-.187,0-3.207" transform="translate(-235.096 -169.284)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_50" data-name="Line 50" x2="1.151" y2="2.659" transform="translate(105.346 62.307)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_51" data-name="Line 51" x1="0.998" y1="2.811" transform="translate(104.979 62.391)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_52" data-name="Line 52" x2="3.778" y2="4.388" transform="translate(100.022 58.002)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_53" data-name="Line 53" x2="5.054" y2="3.921" transform="translate(97.907 58.914)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_54" data-name="Line 54" x2="4.43" y2="2.475" transform="translate(97.585 61.122)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1134" data-name="Path 1134" d="M281,187.221l2.506-2.177,3.986-1.22,3.907-.09,2.87,1.185,3.029,1.945,4.593,1.016-.253.409-1.03-.08-.527.236-.6.024-.5.322-.693.1-.42.381-.648.121-.364.482-.655.159-.3.509-.589.253-.225.537-.523.316-.142.527-.541.447-.107.565-.423.42-.052.565-.378.5.024.589-.291.527.042.645-.17.548.066.357-.392-.083" transform="translate(-193.993 -126.845)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_55" data-name="Line 55" x2="0.607" y2="0.114" transform="translate(98.098 69.372)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_56" data-name="Line 56" x2="0.52" y2="0.18" transform="translate(98.372 67.691)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1135" data-name="Path 1135" d="M273.531,233.336l1-.243.111.243" transform="translate(-188.838 -160.921)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1136" data-name="Path 1136" d="M309.827,192.288l1.425-.447.551-3.439,2.073-.839" transform="translate(-213.895 -129.488)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1137" data-name="Path 1137" d="M303.154,189.483l1-.541.624-2.284,1.8-.731,1.154-1.141" transform="translate(-209.289 -127.571)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1138" data-name="Path 1138" d="M298.072,188.613l.575-.333.8-2.021,1.425-.489.357-.714.478-.121,2.516-.149" transform="translate(-205.78 -127.571)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1139" data-name="Path 1139" d="M281.272,196.413l-1.335-.555-1.373.312-1.622-.159-.78.576-1.265.447.135,1.335.534.53" transform="translate(-189.781 -135.215)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_57" data-name="Line 57" x2="6.922" y2="2.454" transform="translate(93.976 62.131)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1140" data-name="Path 1140" d="M305.74,232.575l1.023.51-.558-1" transform="translate(-211.074 -160.225)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1141" data-name="Path 1141" d="M296.437,215.148l-.069,1.581-1.151,1.2.707.867,1.511.2-1.355,1-1.577-.111-.887-.732" transform="translate(-202.704 -148.532)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1142" data-name="Path 1142" d="M307.106,220.369l.711.357.021.555-.177.468.423.312.863-1,.291-.783-.669-1.459-.575-.291-.579.6,1.269,1.151-.357.87" transform="translate(-212.017 -150.866)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1143" data-name="Path 1143" d="M304.587,216.543l1.248-.891,1.55.333.936,1.754" transform="translate(-210.278 -148.88)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1144" data-name="Path 1144" d="M299.359,205.677l1.376,1.823,1.886.132.4.53.472-.062" transform="translate(-206.669 -141.993)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1145" data-name="Path 1145" d="M297.209,206.684l.156,1.778,1.134.777,1.931.4" transform="translate(-205.185 -142.689)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1146" data-name="Path 1146" d="M289.391,203.01l-3.089-3.2-2.777.773-2.042-.264-1.376,1.109-.887.2L278.9,202.7l1.023,1.414.513-.128-.489-1.154,1.244-.579,1.331.579,2.4-1.043,1.109-.87" transform="translate(-192.548 -137.943)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1147" data-name="Path 1147" d="M284.63,197.845l-1.445-.912-2.063.755-1.355-.2-1.421.645-1.511.6.087,1.8.468,1.158.489-.378" transform="translate(-191.118 -135.957)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1148" data-name="Path 1148" d="M283.652,208.979l2.177,1.355-.094,1.113.8.756-.263,1.688,1.307-.357.711-1.643-1.172-1.647.243-1.085.53-.132" transform="translate(-195.825 -144.273)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1149" data-name="Path 1149" d="M300,248.073l-.714,1-.024,1.334-2.485-.114" transform="translate(-204.883 -171.263)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1150" data-name="Path 1150" d="M303.871,245.062l1.026.933.6-.09" transform="translate(-209.783 -169.184)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1151" data-name="Path 1151" d="M297.725,239.494l.042,1.244-1.4.846.09.575.887-.222-.69,1.043-1.356-.111-.53-1.6.711-1.331-.09-.981" transform="translate(-203.5 -164.971)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1152" data-name="Path 1152" d="M302.438,239.263l1.445,1.064.887-.468" transform="translate(-208.794 -165.18)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1153" data-name="Path 1153" d="M299.426,237.247l.666.731,1.289-.489.756.648" transform="translate(-206.715 -163.789)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1154" data-name="Path 1154" d="M291.5,224.037l-.4,1.248-1.13.686,1.421,1.577,1.8-.464" transform="translate(-200.184 -154.669)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1155" data-name="Path 1155" d="M293.985,253.658l1.106-.957.069-.821" transform="translate(-202.959 -173.891)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1156" data-name="Path 1156" d="M290.38,240.4l.78.534-.555,1.751.891,1.289" transform="translate(-200.47 -165.968)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1157" data-name="Path 1157" d="M289.313,232.87l-1.043,1.064.527,1.269-.711.978" transform="translate(-198.886 -160.767)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1158" data-name="Path 1158" d="M285.152,239.238l.333-1.29.932-.2" transform="translate(-196.861 -164.136)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1159" data-name="Path 1159" d="M285.659,256.537l-1.178,1.47.312.419.846-.513-.472,1.314-1.13-.111-1.047-1.022.957-1.8-1.023-.978" transform="translate(-195.323 -176.263)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1160" data-name="Path 1160" d="M289.579,251.734l-.621,1.463,1.508,1.491h1.29l.156-1.31-.731.513-.135-.8" transform="translate(-199.489 -173.79)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1161" data-name="Path 1161" d="M275.915,257.69,275.25,259l-1,1.154.887,1.487,1.314.246.843-1.245-1.31-.135,1.019-.777" transform="translate(-189.333 -177.902)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1162" data-name="Path 1162" d="M274.472,248.219l1.6,1.889.887-.62.6-.714-.863.18-.558-1.248,1.931-.218.669-.78" transform="translate(-189.487 -170.32)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_58" data-name="Line 58" y1="0.159" x2="2.198" transform="translate(85.896 73.923)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_59" data-name="Line 59" y1="0.912" x2="2.132" transform="translate(85.983 74.457)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_60" data-name="Line 60" x2="2.288" y2="0.468" transform="translate(85.761 73.279)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1163" data-name="Path 1163" d="M277.483,232.652l.666-.291-.312-.711" transform="translate(-191.566 -159.925)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1164" data-name="Path 1164" d="M281.7,219.895l-1.425.707.426.513-.4,1.622,1.265,2.018.135,1.955.329.957" transform="translate(-193.491 -151.809)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1165" data-name="Path 1165" d="M279.566,218.383l.128.665.381.423" transform="translate(-193.004 -150.765)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1166" data-name="Path 1166" d="M279.1,215.372l-.135.51-.489.354,1.355.135,1.043.534" transform="translate(-192.254 -148.686)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1167" data-name="Path 1167" d="M285.152,187.578l2.066-.451.534-1.619,1.931-.509,1.861-1.075" transform="translate(-196.861 -126.976)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1168" data-name="Path 1168" d="M276.734,194.974l-2.6-.336-.312.6-.818.8.243,1.092-.354.576.489,1.532.777.62.288-.2" transform="translate(-188.398 -134.372)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1169" data-name="Path 1169" d="M272.41,210.213l-.045.908-.4,1,.333.551-1.713,3.962.18.711,1.487.107.468.111-.246.711.288.666-.354.707.489.756.156.267-1.265,2.066.218,2.246L270.9,226.6l.312,1.134-.066.887,1.4.756,4.045-1.487,3.421-2.867,2.551-2.111-.045-1.356.555-.932-.288-1.109.2-.534-.4-1.175,1.418-.62.579-1.383.419-.551-.152-1.487.336-.537-.024-1.463-1.736-.908.125-1.06,3.116.9" transform="translate(-186.805 -144.838)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_61" data-name="Line 61" x1="3.785" y2="4.388" transform="translate(112.172 57.974)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_62" data-name="Line 62" x1="5.051" y2="3.924" transform="translate(113.017 58.886)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_63" data-name="Line 63" x1="4.434" y2="2.475" transform="translate(113.96 61.098)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1170" data-name="Path 1170" d="M370.113,187.143l-2.51-2.18-3.987-1.22-3.907-.086-2.867,1.182-3.029,1.945-4.524,1.005.284.42.925-.076.527.236.6.024.506.322.69.1.423.381.645.118.364.485.655.159.3.509.589.25.225.541.523.315.146.527.538.447.107.562.423.42.052.568.378.5-.024.589.288.527-.038.645.17.551-.066.354.392-.083" transform="translate(-241.14 -126.791)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_64" data-name="Line 64" x1="0.607" y2="0.111" transform="translate(117.271 69.348)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_65" data-name="Line 65" x1="0.52" y2="0.177" transform="translate(117.087 67.666)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1171" data-name="Path 1171" d="M421.524,233.247l-1-.243-.111.243" transform="translate(-290.242 -160.859)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1172" data-name="Path 1172" d="M378.674,192.2l-1.425-.444-.551-3.442-2.073-.839" transform="translate(-258.63 -129.426)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1173" data-name="Path 1173" d="M384.171,189.4l-1-.534-.624-2.288-1.8-.735-1.154-1.137" transform="translate(-262.062 -127.517)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1174" data-name="Path 1174" d="M385.749,188.535l-.579-.333-.8-2.021-1.418-.492-.36-.707-.478-.125-2.517-.149" transform="translate(-262.062 -127.517)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1175" data-name="Path 1175" d="M402.054,196.335l1.331-.555,1.376.308,1.622-.156.777.579,1.265.444-.132,1.335-.538.53" transform="translate(-277.566 -135.161)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_66" data-name="Line 66" x1="6.919" y2="2.454" transform="translate(115.08 62.103)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1176" data-name="Path 1176" d="M389.518,232.485,388.5,233l.555-1" transform="translate(-268.206 -160.163)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1177" data-name="Path 1177" d="M392.585,215.07l.063,1.577,1.158,1.2-.711.867-1.508.2,1.352,1,1.577-.111.891-.735" transform="translate(-270.34 -148.478)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1178" data-name="Path 1178" d="M385.68,220.283l-.714.354-.021.555.177.472-.42.308-.867-1-.288-.78.665-1.463.579-.291.575.6-1.269,1.154.357.867" transform="translate(-264.79 -150.804)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1179" data-name="Path 1179" d="M384.617,216.461l-1.248-.887-1.55.336-.936,1.751" transform="translate(-262.951 -148.826)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1180" data-name="Path 1180" d="M388.956,205.6l-1.373,1.823-1.889.132-.4.534-.472-.069" transform="translate(-265.671 -141.939)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1181" data-name="Path 1181" d="M393.139,206.595l-.153,1.782-1.137.773-1.931.4" transform="translate(-269.188 -142.627)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1182" data-name="Path 1182" d="M384.757,202.92l3.089-3.2,2.776.78,2.045-.271,1.373,1.109.891.2.312,1.068-1.023,1.421-.513-.135.489-1.151-1.241-.582-1.335.582-2.4-1.047-1.109-.867" transform="translate(-265.625 -137.882)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1183" data-name="Path 1183" d="M395.527,197.763l1.442-.908,2.062.752,1.359-.2,1.418.645,1.511.6-.087,1.8-.468,1.158-.489-.381" transform="translate(-273.06 -135.903)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1184" data-name="Path 1184" d="M403.535,208.9l-2.177,1.355.094,1.109-.8.755.271,1.688-1.31-.357-.711-1.643,1.175-1.643-.243-1.085-.534-.135" transform="translate(-275.387 -144.219)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1185" data-name="Path 1185" d="M390.355,248l.714.995.021,1.338,2.485-.114" transform="translate(-269.489 -171.209)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1186" data-name="Path 1186" d="M390.04,244.983l-1.023.933-.6-.09" transform="translate(-268.153 -169.129)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1187" data-name="Path 1187" d="M393.12,239.416l-.045,1.244,1.4.842-.09.575-.887-.222.69,1.047,1.356-.111.534-1.6-.714-1.331.09-.978" transform="translate(-271.367 -164.917)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1188" data-name="Path 1188" d="M389.9,239.173l-1.442,1.068-.887-.471" transform="translate(-267.565 -165.118)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1189" data-name="Path 1189" d="M392.058,237.169l-.665.731-1.286-.489-.759.645" transform="translate(-268.794 -163.735)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1190" data-name="Path 1190" d="M398.85,223.959l.406,1.244,1.126.69-1.418,1.577-1.8-.468" transform="translate(-274.189 -154.614)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1191" data-name="Path 1191" d="M400.923,253.576l-1.106-.953-.069-.821" transform="translate(-275.974 -173.836)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1192" data-name="Path 1192" d="M404.659,240.315l-.78.534.555,1.754-.891,1.29" transform="translate(-278.594 -165.906)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1193" data-name="Path 1193" d="M405.479,232.792l1.047,1.064-.53,1.265.711.978" transform="translate(-279.931 -160.713)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1194" data-name="Path 1194" d="M409.563,239.152l-.333-1.29-.929-.2" transform="translate(-281.879 -164.075)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1195" data-name="Path 1195" d="M405.771,256.462l1.178,1.463-.312.423-.839-.513.464,1.314,1.13-.111,1.047-1.022-.957-1.8,1.023-.977" transform="translate(-280.132 -176.209)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1196" data-name="Path 1196" d="M401.372,251.645l.62,1.466-1.508,1.491h-1.29l-.152-1.31.731.51.135-.8" transform="translate(-275.487 -173.728)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1197" data-name="Path 1197" d="M414.826,257.6l.665,1.31,1,1.154-.887,1.494-1.31.239-.846-1.245,1.31-.135-1.019-.773" transform="translate(-285.434 -177.84)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1198" data-name="Path 1198" d="M413.55,248.141l-1.6,1.886-.887-.62-.6-.711.86.177.558-1.241-1.931-.225-.669-.777" transform="translate(-282.559 -170.266)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_67" data-name="Line 67" x1="2.201" y1="0.156" transform="translate(127.881 73.899)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_68" data-name="Line 68" x1="2.132" y1="0.908" transform="translate(127.86 74.433)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_69" data-name="Line 69" x1="2.284" y2="0.468" transform="translate(127.93 73.254)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1199" data-name="Path 1199" d="M418.561,232.562l-.665-.288.312-.714" transform="translate(-288.503 -159.863)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1200" data-name="Path 1200" d="M411.925,219.816l1.421.7-.426.513.4,1.622-1.262,2.021-.135,1.955-.333.953" transform="translate(-284.151 -151.755)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1201" data-name="Path 1201" d="M416.829,218.294l-.132.666-.381.426" transform="translate(-287.413 -150.704)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1202" data-name="Path 1202" d="M413.076,215.282l.135.51.489.357-1.355.135-1.043.53" transform="translate(-283.95 -148.625)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1203" data-name="Path 1203" d="M398.131,187.5l-2.066-.447-.534-1.619-1.927-.51-1.861-1.075" transform="translate(-270.448 -126.922)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1204" data-name="Path 1204" d="M412.241,194.892l2.6-.333.312.6.818.8-.243,1.085.357.579-.492,1.532-.777.62-.288-.2" transform="translate(-284.599 -134.318)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1205" data-name="Path 1205" d="M389.271,210.123l.045.908.4,1-.336.555,1.713,3.959-.18.711-1.487.107-.468.111.246.711-.288.666.354.711-.485.755-.159.264,1.265,2.07-.218,2.243,1.109,1.622-.312,1.13.066.891-1.4.752-4.045-1.484-3.418-2.867-2.551-2.111.042-1.356-.555-.929.291-1.116-.2-.53.4-1.178-1.421-.62-.579-1.376-.419-.558.152-1.487-.333-.53.021-1.469,1.736-.908-.125-1.06-3.116.9" transform="translate(-258.901 -144.776)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_70" data-name="Line 70" y2="0.659" transform="translate(104.084 86.142)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1206" data-name="Path 1206" d="M333.376,255.487l-.631,1.234.024.107-.25-.184-.17-.225-.18-.316.09-.388.01-.024-.132-.517.121-.513.01-.024-.132-.513.121-2.6" transform="translate(-229.299 -173.643)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1207" data-name="Path 1207" d="M337.636,257.858l.769,3.4.5-.243-.2-2.565" transform="translate(-233.094 -178.018)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_71" data-name="Line 71" x2="0.887" y2="3.844" transform="translate(103.911 80.984)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1208" data-name="Path 1208" d="M332.6,260.312l.534.374,1.165-.79" transform="translate(-229.616 -179.425)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1209" data-name="Path 1209" d="M332.217,269l.645,1.22,1.009-.8" transform="translate(-229.353 -185.708)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_72" data-name="Line 72" y2="2.444" transform="translate(102.871 82.461)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1210" data-name="Path 1210" d="M332.52,254.925l2.839,1.622-.094-1.237" transform="translate(-229.562 -175.993)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1211" data-name="Path 1211" d="M334.385,274.147c.329,0,.3.347.3.725,0,.4.028.513-.253.513H331.9c-.295,0-.232-.115-.232-.513,0-.364-.028-.718.218-.718Z" transform="translate(-228.97 -189.263)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1212" data-name="Path 1212" d="M335.1,278.133v.7h-2.51v-.7" transform="translate(-229.608 -192.015)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_73" data-name="Line 73" y2="0.659" transform="translate(111.908 86.142)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_74" data-name="Line 74" x2="0.652" y2="1.886" transform="translate(111.648 81.636)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1213" data-name="Path 1213" d="M356.515,260.084l.35,1.723-.79,1.806-.1-.139-.135-.516.125-.51.011-.028v-.107l-.135-.513.125-1.248.011-.028-.038-.159.017-.038.284-.173.277-.069.468-.281.3.537.222-.315.371-.194.156-.61.326-.187.121-.045h.322" transform="translate(-245.661 -178.798)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_75" data-name="Line 75" x2="0.003" y2="3.331" transform="translate(111.62 81.542)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_76" data-name="Line 76" x1="0.808" y1="1.016" transform="translate(112.213 81.033)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_77" data-name="Line 77" x1="0.25" y1="2.257" transform="translate(110.054 79.286)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1214" data-name="Path 1214" d="M362.886,258.7l-.42-1.21.544-.617.083-.27.267-.27-.1-.388-.007-.024.132-.516-.125-.513-.007-.024.132-.513-.128-2.5" transform="translate(-250.236 -173.867)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_78" data-name="Line 78" y2="2.444" transform="translate(113.125 82.461)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_79" data-name="Line 79" x1="2.905" y2="1.549" transform="translate(110.13 78.932)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1215" data-name="Path 1215" d="M356.446,274.147c-.329,0-.305.347-.305.725,0,.4-.024.513.26.513h2.531c.295,0,.232-.115.232-.513,0-.364.028-.718-.218-.718Z" transform="translate(-245.869 -189.263)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1216" data-name="Path 1216" d="M356.892,278.133v.7h2.51v-.7" transform="translate(-246.388 -192.015)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1217" data-name="Path 1217" d="M344.743,265.628a1.289,1.289,0,0,0-.132.565,1.26,1.26,0,1,0,2.52,0,1.308,1.308,0,0,0-.1-.492" transform="translate(-237.909 -183.382)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1218" data-name="Path 1218" d="M344.725,262.336a1.293,1.293,0,0,0-.114.534,1.26,1.26,0,1,0,2.423-.492" transform="translate(-237.909 -181.109)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1219" data-name="Path 1219" d="M344.743,258.843a1.277,1.277,0,0,0-.132.565,1.26,1.26,0,1,0,2.52,0,1.283,1.283,0,0,0-.1-.492" transform="translate(-237.909 -178.698)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1220" data-name="Path 1220" d="M344.611,254.452a1.26,1.26,0,1,0,1.258-1.262A1.261,1.261,0,0,0,344.611,254.452Z" transform="translate(-237.909 -174.795)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1221" data-name="Path 1221" d="M345.909,174.341v-1.22h1.2v1.22" transform="translate(-238.805 -119.517)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1222" data-name="Path 1222" d="M352.9,174.341v-1.22h1.2v1.22" transform="translate(-243.628 -119.517)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1223" data-name="Path 1223" d="M339,174.341v-1.22h1.2v1.22" transform="translate(-234.037 -119.517)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1224" data-name="Path 1224" d="M360.943,174.354l.426-.492.08-.742H359.8v1.22" transform="translate(-248.397 -119.517)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1225" data-name="Path 1225" d="M331.228,174.341l-.364-.478-.08-.742h1.647v1.22" transform="translate(-228.364 -119.517)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1226" data-name="Path 1226" d="M383.94,296.465l1.556-.17,3.154-2.832.884-1.761" transform="translate(-265.061 -201.382)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1227" data-name="Path 1227" d="M385.518,299.574l1.581-.27,3.8-3.21.558-2" transform="translate(-266.15 -203.036)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1228" data-name="Path 1228" d="M294.892,291.3l.881,1.764,3.158,2.832,1.556.166" transform="translate(-203.585 -201.104)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1229" data-name="Path 1229" d="M292.194,293.694l.555,2,3.806,3.21,1.581.271" transform="translate(-201.722 -202.758)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1230" data-name="Path 1230" d="M294.36,284.367l-.2.738-3.567-3.73-2.61-4.25-.51,1.123,1.432,3.38-.749.721-1.539-3.5-.426,1.709.971,4.229,4.856,4.122,1.348.364,1.113-2.991,1.5-.863" transform="translate(-197.58 -191.319)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1231" data-name="Path 1231" d="M312.525,288.544l3.716,2.392,5.005,1.47h2.777" transform="translate(-215.758 -199.203)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1232" data-name="Path 1232" d="M360.04,289.149l-3.7,2.2-4.648,1.47h-2.437" transform="translate(-241.116 -199.62)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1233" data-name="Path 1233" d="M356.342,296.269l-4.648,1.47h-2.437" transform="translate(-241.117 -204.536)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1234" data-name="Path 1234" d="M310.733,294.344l4.271,2.7,4.829,1.5h2.776" transform="translate(-214.521 -203.206)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1235" data-name="Path 1235" d="M360.2,294.344l-4.257,2.7-4.825,1.5h-2.437" transform="translate(-240.722 -203.206)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1236" data-name="Path 1236" d="M355.948,303.076l-4.825,1.5h-2.437" transform="translate(-240.722 -209.235)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1237" data-name="Path 1237" d="M308.058,297.859l5.1,3.161,4.829,1.293h2.777" transform="translate(-212.674 -205.633)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1238" data-name="Path 1238" d="M320.183,287.884h-2.076l-4.021-1.075-3.6-2.08-.1-.1-.53-.839-.943,2.562-.426,1.369-.77.468" transform="translate(-212.434 -195.918)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1239" data-name="Path 1239" d="M360.888,297.691l-4.94,3.213-4.825,1.293h-2.437" transform="translate(-240.723 -205.517)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1240" data-name="Path 1240" d="M347.846,287.884h2.333l4.025-1.075,3.6-2.08.1-.1.534-.839.939,2.562.43,1.369.444.357" transform="translate(-240.143 -195.918)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1241" data-name="Path 1241" d="M388.352,280.554l-.967,4.229-4.857,4.122-1.352.364-1.068-2.856-1.678-.977,1.661-1.068.717.513,3.144-3.5,2.61-4.25.51,1.123-1.342,3.39.839.832,1.355-3.626.426,1.709" transform="translate(-261.258 -191.319)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1242" data-name="Path 1242" d="M355.948,308.069l-4.825,1.293h-2.437" transform="translate(-240.722 -212.682)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1243" data-name="Path 1243" d="M348.686,287.884h2.073l4.025-1.075,3.6-2.08.1-.1.534-.839.939,2.562.43,1.369.5.374" transform="translate(-240.722 -195.918)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1244" data-name="Path 1244" d="M353.253,177.106s.052,3.064-.6,3.564a26.2,26.2,0,0,1-3.987,1.705s.007-2.558.582-3.421a6.9,6.9,0,0,1,1.986-1.848" transform="translate(-240.707 -122.269)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_80" data-name="Line 80" x1="4.905" transform="translate(107.641 54.837)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1245" data-name="Path 1245" d="M332.225,177.106s-.055,3.064.6,3.564a26.956,26.956,0,0,0,4.295,1.709,11.9,11.9,0,0,0-.9-3.425,6.818,6.818,0,0,0-1.983-1.848" transform="translate(-229.358 -122.269)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_81" data-name="Line 81" x2="4.898" transform="translate(102.868 54.837)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_82" data-name="Line 82" x1="2.104" y1="0.104" transform="translate(103.322 78.211)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_83" data-name="Line 83" y1="0.194" x2="1.064" transform="translate(104.448 76.547)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_84" data-name="Line 84" x2="2.51" y2="1.085" transform="translate(101.374 73.701)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1246" data-name="Path 1246" d="M327.963,242.832l2.447-.177.8,3.113" transform="translate(-226.416 -167.522)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_85" data-name="Line 85" x1="2.125" y2="2.326" transform="translate(100.296 72.61)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_86" data-name="Line 86" x2="0.451" y2="2.839" transform="translate(102.538 75.182)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_87" data-name="Line 87" x2="0.527" y2="0.049" transform="translate(99.516 72.977)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1247" data-name="Path 1247" d="M323.1,235.848l1.331.676.222,3.016" transform="translate(-223.062 -162.823)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_88" data-name="Line 88" x1="0.86" y2="3.078" transform="translate(101.807 66.502)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_89" data-name="Line 89" x1="0.697" y1="1.192" transform="translate(100.92 65.521)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_90" data-name="Line 90" x1="1.192" y2="0.017" transform="translate(103.322 63.247)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1248" data-name="Path 1248" d="M330.448,203.366l1.619.336,1.026-1.809" transform="translate(-228.132 -139.381)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_91" data-name="Line 91" x2="1.948" transform="translate(103.908 64.879)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1249" data-name="Path 1249" d="M329.553,209.84l1.466.392.482-2.5" transform="translate(-227.514 -143.415)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1250" data-name="Path 1250" d="M321.425,212.266l2.517-2-.35-1.914" transform="translate(-221.902 -143.84)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_92" data-name="Line 92" x1="0.7" y1="1.99" transform="translate(100.604 67.552)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_93" data-name="Line 93" x2="3.137" y2="0.055" transform="translate(99.263 72.308)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_94" data-name="Line 94" x2="2.964" y2="0.003" transform="translate(99.263 69.618)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1251" data-name="Path 1251" d="M307.966,260.541l-.669-.125-.426-.8.368-.835.877-.236.732.541.035.908-.361.312.011.014,1.6,2.246,4.305,3.47,4.711,1.109-.017-.014v-4.766" transform="translate(-211.855 -178.489)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1252" data-name="Path 1252" d="M345.707,293.486l-.156-.815.589-.69.908.028.544.731-.187.717" transform="translate(-238.558 -201.575)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1253" data-name="Path 1253" d="M350.244,262.253v4.766l-.017.014,4.711-1.109,4.3-3.47,1.529-2.159.076-.069-.454-.392.035-.908.731-.541.88.232.361.835-.423.8-.458.087.042.035-1.321,2.77-4.742,3.824-5.432,1.324-.011-.01-.215.53" transform="translate(-241.518 -178.381)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1254" data-name="Path 1254" d="M321.983,273.248l-.215-.53-.011.011-5.432-1.324-4.745-3.823-1.317-2.77" transform="translate(-214.197 -182.818)" fill="none" stroke="#fff" stroke-width="1"/>
        <path id="Path_1255" data-name="Path 1255" d="M343.558,225.989l.052-3.217,1.088.035s.912.087.912.856a.791.791,0,0,1-.943.8l-.891-.052,2.707,1.7" transform="translate(-237.182 -153.795)" fill="none" stroke="#fff" stroke-width="1"/>
        <line id="Line_95" data-name="Line 95" x1="0.052" y2="2.198" transform="translate(107.887 70.602)" fill="none" stroke="#fff" stroke-width="1"/>
      </g>
    </g>
  </g>
</svg>
