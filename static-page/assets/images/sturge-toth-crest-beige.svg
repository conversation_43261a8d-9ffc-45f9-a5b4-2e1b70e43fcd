<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="124.219" height="124.222" viewBox="0 0 124.219 124.222">
    <defs>
        <clipPath id="clip-path">
            <rect id="Rectangle_846" data-name="Rectangle 846" width="124.219" height="124.221" fill="none" stroke="#f7f5f3" stroke-width="0.3"/>
        </clipPath>
    </defs>
    <g id="Group_1059" data-name="Group 1059" transform="translate(-1457.896 -4621.404)">
        <line id="Line_27" data-name="Line 27" y2="0.441" transform="translate(1522.979 4715.244)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
        <path id="Path_919" data-name="Path 919" d="M390.467,163.325l.183.564h.593l-.478.346.183.564-.481-.349-.478.349.183-.564-.478-.346h.59l.183-.564" transform="translate(1167.862 4499.848)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
        <path id="Path_920" data-name="Path 920" d="M304.288,163.325l.183.564h.593l-.478.346.181.564-.478-.349-.478.349.183-.564-.481-.346h.593l.183-.564" transform="translate(1232.004 4499.848)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
        <path id="Path_921" data-name="Path 921" d="M368.717,155.79l.183.564h.593l-.478.349.18.561-.478-.347-.478.347.183-.561-.481-.349h.593l.183-.564" transform="translate(1184.052 4505.455)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
        <path id="Path_922" data-name="Path 922" d="M322.884,155.79l.18.564h.593l-.478.349.183.561-.478-.347-.481.347.183-.561-.478-.349h.593l.183-.564" transform="translate(1218.164 4505.455)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
        <path id="Path_923" data-name="Path 923" d="M345.652,151.57l.183.564h.592l-.478.347.183.564-.481-.349-.478.349.183-.564-.478-.347h.593l.18-.564" transform="translate(1201.217 4508.597)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
        <line id="Line_28" data-name="Line 28" x1="0.172" y2="1.076" transform="translate(1549.844 4687.234)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
        <line id="Line_29" data-name="Line 29" x1="1.268" y2="1.964" transform="translate(1547.063 4678.447)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
        <g id="Group_1020" data-name="Group 1020" transform="translate(1457.896 4621.404)">
            <g id="Group_1019" data-name="Group 1019" clip-path="url(#clip-path)">
                <path id="Path_924" data-name="Path 924" d="M344.97,222.634a1.831,1.831,0,1,0-1.83,1.83A1.832,1.832,0,0,0,344.97,222.634Z" transform="translate(-254.022 -164.334)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_30" data-name="Line 30" y1="0.06" x2="2.528" transform="translate(81.903 59.322)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_31" data-name="Line 31" y1="0.083" x2="2.554" transform="translate(81.903 57.79)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_32" data-name="Line 32" x2="2.471" transform="translate(93.997 59.382)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_33" data-name="Line 33" y1="0.083" x2="2.554" transform="translate(93.997 57.79)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_34" data-name="Line 34" x1="0.02" y2="1.403" transform="translate(87.174 68.679)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_925" data-name="Path 925" d="M98.08,234.682c-.063-.069-.089-.172-.215-.189-.026.135.083.18.152.249C98.006,234.688,98.029,234.668,98.08,234.682Z" transform="translate(-72.835 -174.524)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_926" data-name="Path 926" d="M98.524,235.219c-.052-.014-.074.006-.063.06C98.484,235.259,98.5,235.236,98.524,235.219Z" transform="translate(-73.279 -175.061)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_927" data-name="Path 927" d="M114.614,182.85a.331.331,0,0,1,.175.054c.086.043.172.14.258.034a.256.256,0,0,0-.014-.343,1.47,1.47,0,0,1-.218-.332,4.765,4.765,0,0,1-.327-2.789c.072-.656.2-1.309.22-1.976a2.111,2.111,0,0,0-.512-1.595,3.53,3.53,0,0,0-1.543-.782c-.129-.043-.2.031-.238.146a1.263,1.263,0,0,0-.054.289.766.766,0,0,1-1.2.607,1.868,1.868,0,0,0-1.076-.286c-.1.009-.189.037-.215.129s.089.12.152.158c.289.172.63.226.922.409a3.134,3.134,0,0,1,1.323,1.726,15.794,15.794,0,0,1,.478,2.336,2.828,2.828,0,0,0,.381,1.3" transform="translate(-81.767 -130.327)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_928" data-name="Path 928" d="M145.415,188.881a1.737,1.737,0,0,1,.573.378c.249.341.618.352.956.444.117.034.309.063.349-.092.031-.135-.132-.212-.246-.281s-.263-.146-.4-.22A.1.1,0,0,1,146.6,189c.017-.057.072-.04.112-.043a1.052,1.052,0,0,1,.381.117,1.785,1.785,0,0,0,1.271-.006.4.4,0,0,0,.243-.352c.023-.14-.112-.186-.212-.241-.455-.252-.959-.363-1.434-.573a5.54,5.54,0,0,1-1.463-.885,3.452,3.452,0,0,1-.822-1.114,2.067,2.067,0,0,1-.071-1.855.725.725,0,0,0-.1-.876,2.755,2.755,0,0,1-.673-1.414,2.514,2.514,0,0,0-.324-1.139c-.152-.206-.306-.413-.447-.624a2.236,2.236,0,0,1-.409-.7,2.2,2.2,0,0,1-.14-.962c0-.14,0-.22,0-.3a1.563,1.563,0,0,0-.218-.836.725.725,0,0,0-.719-.4c-.023,0-.046-.02-.072-.032-.092-.037-.186-.074-.272.014s-.052.2-.006.3a2.414,2.414,0,0,1,.206.4,16.759,16.759,0,0,1,.564,2.454,19.127,19.127,0,0,1,.086,2.56c-.014.4.034.452.444.384a.611.611,0,0,1,.489.049.739.739,0,0,1,.163.824c-.086.272-.312.218-.507.229a1.171,1.171,0,0,1-.261-.043c-.215-.028-.369.1-.318.306a6.976,6.976,0,0,0,.567,1.953,6.481,6.481,0,0,0,.979,1.317" transform="translate(-105.072 -131.529)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_929" data-name="Path 929" d="M130.5,185.07c.074.092.195.189.321.08s.04-.2-.014-.312a4.914,4.914,0,0,1-.355-.747c-.158-.478-.266-.973-.418-1.452a13.206,13.206,0,0,1-.295-1.858c-.063-.384.012-.79-.069-1.168-.149-.71-.092-1.423-.172-2.133.037-.478-.026-.541-.513-.535a1.734,1.734,0,0,1-.555-.049c-.12-.037-.269-.192-.361-.066s.092.238.158.355a1.835,1.835,0,0,1,.381.8,16.845,16.845,0,0,1-.169,2.737,4.045,4.045,0,0,0,.2,2.459" transform="translate(-95.295 -131.572)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_930" data-name="Path 930" d="M92.131,263.377a6.089,6.089,0,0,1,1.349-1.978,5.03,5.03,0,0,1,2-1.154c.309-.086.621-.132.928-.206a5.106,5.106,0,0,1,2.728.135,2.294,2.294,0,0,1,.721.252,6.847,6.847,0,0,1,1.016.719,1.7,1.7,0,0,0,.269.249.93.93,0,0,0,.933.034c.272-.126.241-.381.235-.618a.779.779,0,0,0-.157-.355,3.312,3.312,0,0,0-.742-.684,4.765,4.765,0,0,0-1.294-.744A5.474,5.474,0,0,0,98.6,258.6a7.723,7.723,0,0,0-2.119.123,4.878,4.878,0,0,0-1.621.687,6.944,6.944,0,0,0-1.025.716,8.049,8.049,0,0,0-.876.971,8.47,8.47,0,0,0-.7,1.085c-.1.192-.16.392-.258.581a5.458,5.458,0,0,0-.444,1.7c-.009.072-.009.155.083.192.138-.123.009-.332.12-.429C92.028,264,91.993,263.655,92.131,263.377Z" transform="translate(-68.136 -192.454)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_931" data-name="Path 931" d="M108.574,186.943a2.262,2.262,0,0,1-1.254-1.715c-.217-.9-.252-1.838-.512-2.737a3.027,3.027,0,0,0-.862-1.491,2.714,2.714,0,0,0-1.2-.6c-.24-.057-.324.049-.263.286.023.1.04.192.057.281-.04.252-.043.478-.343.63-.364.186-.707.043-1.057.072-.089.012-.183-.031-.226.066s.077.126.132.169a1.074,1.074,0,0,0,.255.135,3.686,3.686,0,0,1,1.875,1.875c.309.616.541,1.268.833,1.892a4.65,4.65,0,0,0,.578,1.005,2.967,2.967,0,0,0,1.245.919c.355.135.392.146.607-.16" transform="translate(-76.588 -134.252)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_932" data-name="Path 932" d="M104.292,192.477a3.865,3.865,0,0,1-.8-.916c-.275-.47-.447-1-.676-1.5a10.759,10.759,0,0,0-.9-1.8,3.909,3.909,0,0,0-2.058-1.354.216.216,0,0,0-.246.043c-.074.086-.009.169.037.241.1.186.22.366.32.553a.612.612,0,0,1-.269.925.457.457,0,0,0-.132.089c-.066.063-.086.177,0,.2.272.063.444.3.7.375a1.412,1.412,0,0,1,.6.39c.587.578,1.154,1.174,1.715,1.784a3.922,3.922,0,0,0,1.16,1.034c.458.215.833.616,1.38.655a.236.236,0,0,1,.143.063.577.577,0,0,0,.378.111c.077.006.183.031.206-.063.029-.115-.089-.129-.169-.163" transform="translate(-74.056 -139.098)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_933" data-name="Path 933" d="M137.207,182.486a1.35,1.35,0,0,0-.232-.7,4.541,4.541,0,0,1-.71-2.416c-.043-.888,0-1.781-.026-2.674a11.635,11.635,0,0,0-.215-2.293c-.1-.441-.235-.873-.366-1.3-.235-.785-.756-1.44-.982-2.225a.291.291,0,0,0-.318-.189.952.952,0,0,1-.286,0c-.226-.057-.183.06-.132.186.132.332.318.656.106,1.031a.376.376,0,0,0,.026.232,16.961,16.961,0,0,1,.349,2.574c.051.458.037.925.1,1.377a18.8,18.8,0,0,0,.481,2.6c.135.447.286.893.449,1.329a7.064,7.064,0,0,0,.782,1.417" transform="translate(-99.656 -127.029)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_934" data-name="Path 934" d="M114.9,227.338a5.73,5.73,0,0,0-.744-1.492,16.944,16.944,0,0,0-1.062-1.486,7.583,7.583,0,0,0-.647-.641,12.769,12.769,0,0,0-1.457-1.142c-.349-.249-.767-.407-1.105-.67a8.358,8.358,0,0,0-1.529-.816c-.258-.126-.564-.2-.822-.321-.432-.195-.9-.258-1.325-.447a6.807,6.807,0,0,0-1.228-.344c-.472-.109-.928-.272-1.394-.409-.535-.158-1.062-.324-1.592-.5-.6-.2-1.157-.481-1.75-.7-.318-.114-.616-.329-.928-.478-.61-.289-1.137-.71-1.712-1.054-.381-.229-.693-.575-1.056-.845A5.147,5.147,0,0,1,95.3,214.5a1.74,1.74,0,0,1-.355-.833c-.009-.2-.22-.358-.195-.6.017-.155.014-.243.209-.132.166.092.243.255.375.364.275.232.559.461.822.7a16.049,16.049,0,0,0,1.652,1.028,13.464,13.464,0,0,0,1.818.908c.587.295,1.2.513,1.807.764.644.269,1.32.452,1.947.75.444.215.916.312,1.36.5s.888.335,1.32.527a6.512,6.512,0,0,0,.667.226c.567.2,1.082.515,1.64.733a18.279,18.279,0,0,1,2.167,1.1,6.629,6.629,0,0,1,.736.5c.24.18.527.309.756.512.361.321.721.639,1.082.962a12.344,12.344,0,0,1,.968,1.048,3.686,3.686,0,0,1,.584.862c.209.45.438.888.647,1.334a6.314,6.314,0,0,1,.252.945,6.046,6.046,0,0,1,.1,2.817c-.057.3-.072.616-.1.925a.236.236,0,0,1-.08.166.334.334,0,0,1-.083-.292A9.677,9.677,0,0,0,114.9,227.338Z" transform="translate(-70.515 -158.447)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_935" data-name="Path 935" d="M106.026,202.788c-.633-.16-1.984-.687-2.617-.859-.312-.086-.644-.217-.962-.323-.753-.252-1.514-.461-2.27-.7-.51-.16-1.028-.318-1.523-.51-.515-.2-1.039-.392-1.543-.63-.43-.2-.859-.407-1.288-.6a6.516,6.516,0,0,1-.793-.493,7.121,7.121,0,0,1-.871-.6c-.366-.327-.787-.578-1.137-.919-.069-.069-.158-.138-.115-.249s.155-.12.269-.126c.573-.012.573-.014.324-.544-.143-.306-.369-.558-.5-.87-.043-.106-.092-.2-.031-.3a.227.227,0,0,1,.269-.063,5.573,5.573,0,0,1,2.208.867,10.893,10.893,0,0,1,1.8,1.772,8.134,8.134,0,0,0,2.245,1.787,3.613,3.613,0,0,0,.922.289,2.883,2.883,0,0,1,1.546.653,2.9,2.9,0,0,0,1.174.725c.862.329,1.752.593,2.582,1,.65.315,1.3.638,1.921,1a1.842,1.842,0,0,1,.87.73,2.827,2.827,0,0,1-.893-.335A12.641,12.641,0,0,0,106.026,202.788Z" transform="translate(-69.139 -145.121)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_936" data-name="Path 936" d="M99.959,187.749c-.1-.186-.218-.366-.321-.553-.046-.072-.112-.154-.037-.24a.215.215,0,0,1,.246-.043,3.91,3.91,0,0,1,2.059,1.354,10.771,10.771,0,0,1,.9,1.8c.229.5.4,1.028.676,1.5a3.849,3.849,0,0,0,.8.916,5.723,5.723,0,0,0,1.377.673c.08.035.2.049.169.163-.023.094-.129.069-.206.063a.577.577,0,0,1-.378-.112.236.236,0,0,0-.143-.063c-.547-.04-.922-.441-1.38-.656a3.924,3.924,0,0,1-1.16-1.033c-.561-.61-1.128-1.206-1.715-1.784a1.414,1.414,0,0,0-.6-.389c-.255-.077-.427-.312-.7-.375-.089-.023-.069-.138,0-.2a.463.463,0,0,1,.132-.089A.612.612,0,0,0,99.959,187.749Z" transform="translate(-74.055 -139.098)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_937" data-name="Path 937" d="M104.485,180.684c-.06-.238.023-.343.263-.286a2.714,2.714,0,0,1,1.2.6,3.027,3.027,0,0,1,.862,1.491c.261.9.3,1.838.512,2.737a2.262,2.262,0,0,0,1.254,1.715c.18.074.218.195.086.355-.074.089-.152.178-.218.269-.215.306-.252.295-.607.16a2.967,2.967,0,0,1-1.245-.919,4.65,4.65,0,0,1-.578-1.005c-.292-.624-.524-1.277-.833-1.892a3.686,3.686,0,0,0-1.875-1.875,1.074,1.074,0,0,1-.255-.135c-.054-.043-.18-.06-.132-.169s.137-.054.226-.066c.349-.029.693.115,1.056-.072.3-.152.3-.378.344-.63C104.525,180.876,104.508,180.782,104.485,180.684Z" transform="translate(-76.588 -134.252)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_938" data-name="Path 938" d="M129.665,179.614c.08.378.006.785.069,1.168a13.2,13.2,0,0,0,.295,1.858c.152.478.261.974.418,1.452a4.94,4.94,0,0,0,.355.747c.054.109.126.218.014.312s-.246.012-.321-.08a1.223,1.223,0,0,0-.427-.306,3.474,3.474,0,0,1-1.434-1.577,4.041,4.041,0,0,1-.2-2.459,16.843,16.843,0,0,0,.169-2.737,1.836,1.836,0,0,0-.381-.8c-.066-.117-.249-.229-.158-.355s.24.029.361.066a1.736,1.736,0,0,0,.555.049c.487-.006.55.057.513.535C129.574,178.191,129.516,178.9,129.665,179.614Z" transform="translate(-95.295 -131.572)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_939" data-name="Path 939" d="M142.653,179.3a2.236,2.236,0,0,0,.409.7c.14.212.295.418.447.624a2.51,2.51,0,0,1,.324,1.14,2.758,2.758,0,0,0,.673,1.414.724.724,0,0,1,.1.876,2.067,2.067,0,0,0,.072,1.855,3.445,3.445,0,0,0,.822,1.114,5.536,5.536,0,0,0,1.463.885c.475.209.979.321,1.434.573.1.054.235.1.212.241a.4.4,0,0,1-.243.352,1.784,1.784,0,0,1-1.271.006,1.055,1.055,0,0,0-.381-.117c-.04,0-.095-.014-.112.043a.1.1,0,0,0,.052.109c.135.074.269.143.4.22s.278.146.246.281c-.04.154-.232.126-.349.092-.338-.092-.707-.1-.956-.444a1.738,1.738,0,0,0-.573-.378,8.734,8.734,0,0,1-1.775-1.372,6.48,6.48,0,0,1-.979-1.317,6.958,6.958,0,0,1-.567-1.953c-.052-.206.1-.335.318-.306a1.169,1.169,0,0,0,.26.043c.195-.011.421.043.507-.229a.74.74,0,0,0-.163-.825.613.613,0,0,0-.49-.049c-.409.069-.458.012-.444-.384a19.084,19.084,0,0,0-.086-2.56,16.718,16.718,0,0,0-.564-2.454,2.467,2.467,0,0,0-.206-.4c-.046-.094-.086-.2.006-.295s.18-.051.272-.014c.026.012.049.035.072.031a.726.726,0,0,1,.719.4,1.561,1.561,0,0,1,.217.836c0,.08,0,.16,0,.3A2.2,2.2,0,0,0,142.653,179.3Z" transform="translate(-105.073 -131.529)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_940" data-name="Path 940" d="M136.024,174.406a11.636,11.636,0,0,1,.215,2.293c.026.893-.017,1.787.026,2.674a4.541,4.541,0,0,0,.71,2.416,1.35,1.35,0,0,1,.232.7,3.023,3.023,0,0,1-.971-1.045,7.07,7.07,0,0,1-.782-1.417c-.163-.435-.315-.882-.45-1.328a18.849,18.849,0,0,1-.481-2.6c-.066-.452-.052-.919-.1-1.377a16.96,16.96,0,0,0-.349-2.574.375.375,0,0,1-.026-.232c.212-.375.026-.7-.106-1.031-.052-.126-.095-.243.132-.186a.959.959,0,0,0,.286,0,.292.292,0,0,1,.318.189c.226.785.747,1.44.982,2.225C135.79,173.533,135.924,173.965,136.024,174.406Z" transform="translate(-99.656 -127.029)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_941" data-name="Path 941" d="M114.71,177.5c-.02.667-.149,1.32-.22,1.976a4.765,4.765,0,0,0,.326,2.789,1.48,1.48,0,0,0,.218.332.255.255,0,0,1,.014.343c-.086.106-.172.009-.258-.034a.329.329,0,0,0-.174-.054,1.759,1.759,0,0,1-1.492-.91,2.827,2.827,0,0,1-.381-1.3,15.793,15.793,0,0,0-.478-2.336,3.132,3.132,0,0,0-1.323-1.726c-.292-.183-.633-.238-.922-.409-.063-.037-.177-.054-.152-.158s.117-.12.215-.129a1.869,1.869,0,0,1,1.077.286.766.766,0,0,0,1.2-.607,1.249,1.249,0,0,1,.054-.289c.034-.115.109-.189.238-.146a3.531,3.531,0,0,1,1.543.782A2.112,2.112,0,0,1,114.71,177.5Z" transform="translate(-81.768 -130.327)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_942" data-name="Path 942" d="M92.861,204.975c.063-.043.132.023.189.066.518.4,1.016.822,1.566,1.177a18.387,18.387,0,0,0,2.812,1.569c.693.292,1.371.63,2.084.862.472.152.933.341,1.409.47.53.149,1.037.366,1.569.5.859.218,1.695.515,2.537.79a10.972,10.972,0,0,1,1.672.644,9.034,9.034,0,0,0,.928.381,9.692,9.692,0,0,1,1.208.607c.3.163.6.378.9.578a19.23,19.23,0,0,1,1.569,1.32,6.455,6.455,0,0,1,.733.885,12.58,12.58,0,0,1,1.417,2.508,1.716,1.716,0,0,1,.063.177c-.006.077.009.14-.054.163-.046.017-.077-.029-.111-.054a1.533,1.533,0,0,1-.292-.375,8.781,8.781,0,0,0-1.051-1.088,11.7,11.7,0,0,0-1.973-1.469,24.174,24.174,0,0,0-2.42-1.323c-.727-.355-1.486-.635-2.224-.968-.464-.206-.953-.375-1.437-.552-.974-.358-1.939-.739-2.912-1.091q-1.177-.425-2.325-.922a18.763,18.763,0,0,1-1.738-.8,12.2,12.2,0,0,1-3.1-2.268,6.945,6.945,0,0,1-1.025-1.572C92.815,205.121,92.786,205.027,92.861,204.975Z" transform="translate(-69.078 -152.545)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_943" data-name="Path 943" d="M94.57,272.067A5.887,5.887,0,0,1,95,270.215a3.231,3.231,0,0,1,.547-.956c.172-.209.269-.47.452-.676a6.6,6.6,0,0,1,1.423-1.254,4.12,4.12,0,0,1,1.557-.467,6.291,6.291,0,0,1,1.592,0,3.561,3.561,0,0,1,.77.209,1.77,1.77,0,0,0,.458.12c.132,0,.2.1.246.255a6.389,6.389,0,0,0-1.766.023,4.1,4.1,0,0,0-2.027,1.231,4.4,4.4,0,0,0-.888,1.569,4.2,4.2,0,0,0,.017,2.666,4.582,4.582,0,0,0,.787,1.549,5.835,5.835,0,0,0,2.4,1.858,6.158,6.158,0,0,0,1.973.4,5.028,5.028,0,0,0,2.743-.607,5.211,5.211,0,0,0,1.91-1.532,6.827,6.827,0,0,0,.69-.939,6.129,6.129,0,0,0,.5-1.16c.017-.075.006-.212.135-.181.1.02.083.138.083.229a7.221,7.221,0,0,1-.366,1.706,4.886,4.886,0,0,1-.91,1.789,3.981,3.981,0,0,1-1.082,1.085c-.172.1-.3.266-.47.387a6.07,6.07,0,0,1-2.359,1.031,5.941,5.941,0,0,1-2.711.054c-.4-.1-.825-.134-1.225-.246a6.743,6.743,0,0,1-1.122-.467,7.773,7.773,0,0,1-1.286-.744,6.543,6.543,0,0,1-2.37-3.247A5.7,5.7,0,0,1,94.57,272.067Z" transform="translate(-70.378 -198.576)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_944" data-name="Path 944" d="M99.432,253.777c.109-.123.158.109.258.137.6.175,1.085.59,1.68.768.347.1.707.177,1.051.275a6.166,6.166,0,0,0,2.084.143,8.027,8.027,0,0,0,2.428-.547,7.476,7.476,0,0,0,2.345-1.437,4.9,4.9,0,0,0,1.188-1.372c.206-.4.472-.782.676-1.191a5.942,5.942,0,0,0,.573-1.864,9.558,9.558,0,0,0,.117-1.432,8.044,8.044,0,0,0-.415-2.4,7.8,7.8,0,0,0-1.088-2.17,10.849,10.849,0,0,0-.862-1.062,11.038,11.038,0,0,0-2.036-1.689c-.364-.235-.762-.407-1.145-.607a19.208,19.208,0,0,0-2.637-1.065c-.541-.195-1.091-.366-1.621-.59a11.061,11.061,0,0,1-2.156-1.151,10.659,10.659,0,0,1-1.764-1.569.336.336,0,0,1-.092-.215c-.069-.069-.178-.115-.152-.249.126.017.152.12.215.189a4.235,4.235,0,0,1,.959.653,5.743,5.743,0,0,0,.859.584,14.5,14.5,0,0,0,1.85.9c1.1.438,2.233.8,3.358,1.165a22.291,22.291,0,0,1,2.219.81,11.028,11.028,0,0,1,1.661.93,13.608,13.608,0,0,1,1.861,1.409,10.663,10.663,0,0,1,1.761,2.122,8.608,8.608,0,0,1,1.211,3.058,9.72,9.72,0,0,1,.146,1.317,6.762,6.762,0,0,1-.229,1.947,8.165,8.165,0,0,1-.593,1.729,8.617,8.617,0,0,1-1.758,2.511,11.65,11.65,0,0,1-1.675,1.26,9.324,9.324,0,0,1-2.073.876,4.656,4.656,0,0,1-1.231.186,10.325,10.325,0,0,1-3.061-.289,10.672,10.672,0,0,1-3.61-1.689.337.337,0,0,1-.12-.135C99.572,253.92,99.346,253.877,99.432,253.777Z" transform="translate(-72.835 -174.525)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_945" data-name="Path 945" d="M107.547,242.773c.355-.263.653-.59.994-.87a5.316,5.316,0,0,0,.527-.661,7.808,7.808,0,0,0,1.288-2.093,10.75,10.75,0,0,0,.466-1.351,15.214,15.214,0,0,0,.321-1.878,4.234,4.234,0,0,0-.077-1.563c-.1-.4-.129-.819-.238-1.22a11.583,11.583,0,0,0-.521-1.392,6.843,6.843,0,0,0-.925-1.5,6.709,6.709,0,0,0-.813-.93c-.306-.269-.578-.587-.888-.867-.421-.384-.893-.7-1.346-1.036a13.18,13.18,0,0,0-1.855-1.059,16.554,16.554,0,0,0-1.692-.7,10.166,10.166,0,0,0-1.5-.541c-.5-.109-.953-.315-1.434-.452a10.89,10.89,0,0,1-1.489-.633,15.419,15.419,0,0,1-1.878-1.016,7.8,7.8,0,0,1-1.457-1.145,4.4,4.4,0,0,1-1.071-1.506,2.569,2.569,0,0,1-.261-1.523,2.1,2.1,0,0,0,.011-.5c0-.089-.014-.2.1-.22a.229.229,0,0,1,.275.157,5,5,0,0,0,1.039,1.589,4.849,4.849,0,0,0,.684.664,10.2,10.2,0,0,0,1.646,1.214c.584.335,1.171.659,1.781.951a20.592,20.592,0,0,0,2.408,1.022,8.119,8.119,0,0,0,1.775.518c.418.043.816.286,1.243.352.386.063.719.278,1.082.361a5.459,5.459,0,0,1,1.3.447,12.428,12.428,0,0,1,1.887.936c.344.243.747.4,1.1.624a6.426,6.426,0,0,1,1.1.87c.249.255.507.5.767.733a2.572,2.572,0,0,1,.372.444,7.936,7.936,0,0,1,1.122,1.8,9.544,9.544,0,0,1,.564,1.841,8.15,8.15,0,0,1,.095,1.669,9.016,9.016,0,0,1-.4,2.313,15.424,15.424,0,0,1-1.071,2.3c-.175.329-.409.616-.593.936-.051.094-.177.143-.258.226-.438.45-.919.853-1.383,1.277-.289.263-.656.4-.965.641a10.248,10.248,0,0,1-1.122.7,1.538,1.538,0,0,1-.616.286c-.054.006-.135.029-.163-.043A.136.136,0,0,1,107.547,242.773Z" transform="translate(-69.701 -162.33)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_946" data-name="Path 946" d="M62.483.5a61.983,61.983,0,1,0,61.98,61.983A61.981,61.981,0,0,0,62.483.5Z" transform="translate(-0.372 -0.372)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_947" data-name="Path 947" d="M72.381,130.96a58.58,58.58,0,1,1,58.576-58.579A58.582,58.582,0,0,1,72.381,130.96Z" transform="translate(-10.271 -10.271)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_948" data-name="Path 948" d="M239.76,182.672l.012-.931c-.049-.08-.1-.155-.152-.235Z" transform="translate(-178.34 -135.089)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_949" data-name="Path 949" d="M328.689,78.5l11.839-12.809c-1.391-1.211-2.852-2.351-4.361-3.421L324.268,79.1A29.544,29.544,0,0,1,328.689,78.5Z" transform="translate(-241.34 -46.341)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_950" data-name="Path 950" d="M41.925,151.344l-4.1-1.89c-.773,1.678-1.477,3.4-2.09,5.159l2.554.79A30.421,30.421,0,0,1,41.925,151.344Z" transform="translate(-26.594 -111.233)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_951" data-name="Path 951" d="M413.832,135.358l4.091-1.758q-1.2-2.543-2.64-4.936l-5.852,3.482A30.026,30.026,0,0,1,413.832,135.358Z" transform="translate(-304.724 -95.76)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_952" data-name="Path 952" d="M24.775,215.1c-.057.472-.123.945-.172,1.426.092-.478.195-.945.306-1.409Z" transform="translate(-18.311 -160.093)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_953" data-name="Path 953" d="M346.364,88.908a29.9,29.9,0,0,1,3.882.275l10.224-8.967q-1.9-2.031-3.985-3.868L345.439,88.94C345.748,88.931,346.052,88.908,346.364,88.908Z" transform="translate(-257.097 -56.823)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_954" data-name="Path 954" d="M248.9,22.032q-2.684-.258-5.44-.261H243.4l.172,13.193a9.246,9.246,0,0,1,3.685.879Z" transform="translate(-181.156 -16.203)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_955" data-name="Path 955" d="M245.162,155.742l1.234-10.419a9.837,9.837,0,0,1-1.368.177Z" transform="translate(-182.365 -108.158)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_956" data-name="Path 956" d="M263.981,39.845l3.645-16.108q-2.68-.541-5.44-.825l-1.294,14.1A9.379,9.379,0,0,1,263.981,39.845Z" transform="translate(-194.172 -17.053)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_957" data-name="Path 957" d="M251.757,150.452c.152-.229.309-.452.469-.676l1.941-8.595a9.027,9.027,0,0,1-1.629.744Z" transform="translate(-187.373 -105.075)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_958" data-name="Path 958" d="M261.937,54.843,260.5,62.067q.6-.687,1.237-1.34l11.2-33q-2.62-.812-5.334-1.377L264.234,43.3a9.3,9.3,0,0,1-2.3,11.544Z" transform="translate(-193.88 -19.611)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_959" data-name="Path 959" d="M393.792,117.776l6.164-3.453q-1.456-2.392-3.13-4.624l-7.816,5.838A29.856,29.856,0,0,1,393.792,117.776Z" transform="translate(-289.526 -81.645)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_960" data-name="Path 960" d="M372.34,102.307l8.237-5.821c-1.126-1.48-2.325-2.9-3.585-4.263l-9.6,8.876A29.568,29.568,0,0,1,372.34,102.307Z" transform="translate(-273.436 -68.638)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_961" data-name="Path 961" d="M33.372,171.418l-2.436-.827a55.206,55.206,0,0,0-1.529,5.362l1.134.226A29.574,29.574,0,0,1,33.372,171.418Z" transform="translate(-21.886 -126.964)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_962" data-name="Path 962" d="M308.294,70.371l12.918-17.3q-2.259-1.589-4.678-2.955L304.475,71.65A29.489,29.489,0,0,1,308.294,70.371Z" transform="translate(-226.609 -37.3)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_963" data-name="Path 963" d="M290.031,64.74,303.3,42.433c-1.606-.9-3.253-1.721-4.947-2.459L287.011,66.383A29.841,29.841,0,0,1,290.031,64.74Z" transform="translate(-213.611 -29.751)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_964" data-name="Path 964" d="M274.364,61.59,287.1,33.967q-2.525-1.083-5.174-1.918l-9.7,31.256C272.912,62.7,273.619,62.125,274.364,61.59Z" transform="translate(-202.604 -23.853)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_965" data-name="Path 965" d="M27.425,192.807l-1.068-.24q-.618,2.7-.971,5.506l.172.017A29.634,29.634,0,0,1,27.425,192.807Z" transform="translate(-18.895 -143.32)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_966" data-name="Path 966" d="M132.686,71.71,120.95,50.755q-2.4,1.37-4.635,2.969l12.6,16.878A29.847,29.847,0,0,1,132.686,71.71Z" transform="translate(-86.569 -37.775)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_967" data-name="Path 967" d="M151.3,66.2,140.277,40.546c-1.681.744-3.327,1.566-4.919,2.468l12.9,21.671A30.2,30.2,0,0,1,151.3,66.2Z" transform="translate(-100.742 -30.177)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_968" data-name="Path 968" d="M65.975,116.281l-7.813-5.832q-1.653,2.216-3.083,4.6l6.25,3.5A29.411,29.411,0,0,1,65.975,116.281Z" transform="translate(-40.992 -82.203)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_969" data-name="Path 969" d="M170.136,62.773l-9.4-30.266q-2.633.85-5.139,1.947l12.311,26.707Q169.062,61.917,170.136,62.773Z" transform="translate(-115.807 -24.194)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_970" data-name="Path 970" d="M114.544,79.489,102.894,63q-2.242,1.611-4.312,3.43l11.63,12.583A29.553,29.553,0,0,1,114.544,79.489Z" transform="translate(-73.371 -46.891)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_971" data-name="Path 971" d="M97,89.484c.04,0,.08.006.117.006L86.278,77.121q-2.07,1.834-3.951,3.857l10.1,8.858A30.489,30.489,0,0,1,97,89.484Z" transform="translate(-61.273 -57.398)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_972" data-name="Path 972" d="M80.805,101.752,71.3,92.962Q69.424,95,67.761,97.214l8.246,5.829A29.74,29.74,0,0,1,80.805,101.752Z" transform="translate(-50.432 -69.188)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_973" data-name="Path 973" d="M52.909,132.875l-5.944-3.539q-1.417,2.383-2.6,4.9l4.292,1.847A30.193,30.193,0,0,1,52.909,132.875Z" transform="translate(-33.018 -96.26)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_974" data-name="Path 974" d="M184.367,47.907a9.27,9.27,0,0,1,1.042-4.277l-3.373-16.955a54.937,54.937,0,0,0-5.311,1.406l10.779,31.757q.726.666,1.4,1.374l-1.234-6.2A9.275,9.275,0,0,1,184.367,47.907Z" transform="translate(-131.53 -19.852)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_975" data-name="Path 975" d="M226.114,148.176c.238.292.461.6.687.9l-.67-7.3a9.231,9.231,0,0,1-1.635-.764Z" transform="translate(-167.084 -104.95)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_976" data-name="Path 976" d="M205.279,37.173l-1.294-14.092c-1.838.2-3.648.487-5.429.856l3.645,16.1A9.357,9.357,0,0,1,205.279,37.173Z" transform="translate(-147.777 -17.178)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_977" data-name="Path 977" d="M226.355,21.794c-1.852.012-3.685.115-5.486.3l1.629,13.8a9.241,9.241,0,0,1,3.682-.908Z" transform="translate(-164.384 -16.22)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_978" data-name="Path 978" d="M236.43,154.535c.049.08.1.155.152.235l.12-9.317a9.4,9.4,0,0,1-1.369-.186Z" transform="translate(-175.149 -108.117)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_979" data-name="Path 979" d="M245.512,299.706l-.014.933c.049.08.1.157.152.235Z" transform="translate(-182.715 -223.059)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_980" data-name="Path 980" d="M109.7,358.291,97.865,371.1q2.091,1.816,4.363,3.421l11.9-16.835A29.9,29.9,0,0,1,109.7,358.291Z" transform="translate(-72.837 -266.212)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_981" data-name="Path 981" d="M425.765,317.114l4.1,1.89a55.039,55.039,0,0,0,2.09-5.156l-2.554-.8A30.08,30.08,0,0,1,425.765,317.114Z" transform="translate(-316.88 -232.992)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_982" data-name="Path 982" d="M47.175,330.941,43.087,332.7a55.049,55.049,0,0,0,2.637,4.936l5.855-3.482A30.057,30.057,0,0,1,47.175,330.941Z" transform="translate(-32.068 -246.306)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_983" data-name="Path 983" d="M95.607,359.526a30.49,30.49,0,0,1-3.885-.272L81.5,368.218q1.9,2.031,3.989,3.871l11.043-12.595C96.22,359.5,95.919,359.526,95.607,359.526Z" transform="translate(-60.656 -267.379)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_984" data-name="Path 984" d="M36.643,313.812l-2.288.713a55.1,55.1,0,0,0,2.122,5.194l3.911-1.8A29.954,29.954,0,0,1,36.643,313.812Z" transform="translate(-25.569 -233.558)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_985" data-name="Path 985" d="M60.065,344.5l-6.164,3.45q1.456,2.4,3.129,4.627l7.816-5.838A29.831,29.831,0,0,1,60.065,344.5Z" transform="translate(-40.116 -256.397)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_986" data-name="Path 986" d="M75.013,354.127l-8.237,5.823c1.122,1.48,2.322,2.9,3.585,4.258l9.6-8.87A29.856,29.856,0,0,1,75.013,354.127Z" transform="translate(-49.699 -263.563)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_987" data-name="Path 987" d="M128.708,352.742l-12.918,17.3q2.259,1.585,4.678,2.952l12.059-21.533A29.78,29.78,0,0,1,128.708,352.742Z" transform="translate(-86.178 -261.579)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_988" data-name="Path 988" d="M148.282,344.183l-13.27,22.306c1.6.9,3.249,1.721,4.944,2.459L151.3,342.54A29.885,29.885,0,0,1,148.282,344.183Z" transform="translate(-100.484 -254.939)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_989" data-name="Path 989" d="M168.063,333.235l-12.732,27.623c1.681.724,3.41,1.36,5.171,1.918l9.7-31.253C169.518,332.124,168.808,332.7,168.063,333.235Z" transform="translate(-115.607 -246.74)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_990" data-name="Path 990" d="M24.6,277.517c.215,1.386.489,2.751.8,4.1l.739-.166A30.116,30.116,0,0,1,24.6,277.517Z" transform="translate(-18.312 -206.545)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_991" data-name="Path 991" d="M305.393,353.085l11.736,20.958c1.594-.916,3.138-1.913,4.632-2.975l-12.6-16.875A29.566,29.566,0,0,1,305.393,353.085Z" transform="translate(-227.292 -262.787)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_992" data-name="Path 992" d="M288.018,344.913l11.023,25.656q2.525-1.121,4.919-2.468l-12.893-21.668A30.906,30.906,0,0,1,288.018,344.913Z" transform="translate(-214.361 -256.705)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_993" data-name="Path 993" d="M388.037,345.927l7.811,5.832q1.653-2.216,3.083-4.6l-6.247-3.5A29.681,29.681,0,0,1,388.037,345.927Z" transform="translate(-288.801 -255.772)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_994" data-name="Path 994" d="M273.274,334.927l9.4,30.266q2.628-.855,5.142-1.947L275.5,336.536Q274.346,335.785,273.274,334.927Z" transform="translate(-203.387 -249.273)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_995" data-name="Path 995" d="M324.716,358.325l11.65,16.483q2.242-1.61,4.315-3.43l-11.63-12.583A30.416,30.416,0,0,1,324.716,358.325Z" transform="translate(-241.673 -266.687)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_996" data-name="Path 996" d="M345.666,359.293l-.115,0,10.843,12.365q2.07-1.834,3.951-3.854l-10.1-8.861A30.523,30.523,0,0,1,345.666,359.293Z" transform="translate(-257.18 -267.146)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_997" data-name="Path 997" d="M366.956,354.693l9.508,8.787q1.868-2.035,3.536-4.252l-8.249-5.829A29.734,29.734,0,0,1,366.956,354.693Z" transform="translate(-273.111 -263.021)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_998" data-name="Path 998" d="M407.931,333.267l5.944,3.539c.945-1.589,1.818-3.224,2.605-4.9l-4.295-1.844A30.07,30.07,0,0,1,407.931,333.267Z" transform="translate(-303.607 -245.648)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_999" data-name="Path 999" d="M28.736,293.885l-.8.158a56.042,56.042,0,0,0,1.552,5.411l2.173-.736A30.291,30.291,0,0,1,28.736,293.885Z" transform="translate(-20.795 -218.727)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1000" data-name="Path 1000" d="M308.294,70.371l12.918-17.3q-2.259-1.589-4.678-2.955L304.475,71.65A29.489,29.489,0,0,1,308.294,70.371Z" transform="translate(-226.609 -37.3)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1001" data-name="Path 1001" d="M328.689,78.5l11.839-12.809c-1.391-1.211-2.852-2.351-4.361-3.421L324.268,79.1A29.544,29.544,0,0,1,328.689,78.5Z" transform="translate(-241.34 -46.341)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1002" data-name="Path 1002" d="M188.908,57.562l-1.234-6.2a9.157,9.157,0,0,0,1.268.9l1.618,7.163c.238.292.461.6.687.9l-.67-7.3a8.925,8.925,0,0,0,1.137.324l1.1,9.268.14,1.165.011-.931.12-9.316c.195.011.389.02.587.02.177,0,.349-.006.524-.014l.135,10.241,1.234-10.419a8.858,8.858,0,0,0,1.134-.315l-.782,8.526c.152-.229.309-.452.47-.676l1.941-8.595a9.256,9.256,0,0,0,1.263-.873l-1.438,7.224q.6-.687,1.237-1.34l11.2-33q-2.62-.812-5.334-1.377l-3.373,16.949a9.2,9.2,0,0,0-.544-.9l3.645-16.108q-2.68-.541-5.44-.825l-1.294,14.1c-.2-.115-.406-.22-.616-.321l1.635-13.811q-2.684-.258-5.44-.261h-.052l.172,13.193c-.095,0-.186-.008-.281-.008-.115,0-.229.006-.344.008l.175-13.187c-1.852.011-3.685.114-5.486.3l1.629,13.8c-.206.1-.412.209-.613.327l-1.294-14.092c-1.838.2-3.648.487-5.428.856l3.645,16.1a9.247,9.247,0,0,0-.544.916l-3.373-16.955a54.79,54.79,0,0,0-5.311,1.406l10.779,31.757Q188.231,56.854,188.908,57.562Z" transform="translate(-131.53 -16.203)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1003" data-name="Path 1003" d="M290.031,64.74,303.3,42.433c-1.606-.9-3.253-1.721-4.947-2.459L287.011,66.383A29.841,29.841,0,0,1,290.031,64.74Z" transform="translate(-213.611 -29.751)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1004" data-name="Path 1004" d="M346.364,88.908a29.9,29.9,0,0,1,3.882.275l10.224-8.967q-1.9-2.031-3.985-3.868L345.439,88.94C345.748,88.931,346.052,88.908,346.364,88.908Z" transform="translate(-257.097 -56.823)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1005" data-name="Path 1005" d="M274.364,61.59,287.1,33.967q-2.525-1.083-5.174-1.918l-9.7,31.256C272.912,62.7,273.619,62.125,274.364,61.59Z" transform="translate(-202.604 -23.853)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1006" data-name="Path 1006" d="M170.136,62.773l-9.4-30.266q-2.633.85-5.139,1.947l12.311,26.707Q169.062,61.917,170.136,62.773Z" transform="translate(-115.807 -24.194)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1007" data-name="Path 1007" d="M393.792,117.776l6.164-3.453q-1.456-2.392-3.13-4.624l-7.816,5.838A29.856,29.856,0,0,1,393.792,117.776Z" transform="translate(-289.526 -81.645)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1008" data-name="Path 1008" d="M372.34,102.307l8.237-5.821c-1.126-1.48-2.325-2.9-3.585-4.263l-9.6,8.876A29.568,29.568,0,0,1,372.34,102.307Z" transform="translate(-273.436 -68.638)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1009" data-name="Path 1009" d="M151.3,66.2,140.277,40.546c-1.681.744-3.327,1.566-4.919,2.468l12.9,21.671A30.2,30.2,0,0,1,151.3,66.2Z" transform="translate(-100.742 -30.177)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1010" data-name="Path 1010" d="M413.832,135.358l4.091-1.758q-1.2-2.543-2.64-4.936l-5.852,3.482A30.026,30.026,0,0,1,413.832,135.358Z" transform="translate(-304.724 -95.76)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1011" data-name="Path 1011" d="M273.274,334.927l9.4,30.266q2.628-.855,5.142-1.947L275.5,336.536Q274.346,335.785,273.274,334.927Z" transform="translate(-203.387 -249.273)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1012" data-name="Path 1012" d="M288.018,344.913l11.023,25.656q2.525-1.121,4.919-2.468l-12.893-21.668A30.906,30.906,0,0,1,288.018,344.913Z" transform="translate(-214.361 -256.705)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1013" data-name="Path 1013" d="M324.716,358.325l11.65,16.483q2.242-1.61,4.315-3.43l-11.63-12.583A30.416,30.416,0,0,1,324.716,358.325Z" transform="translate(-241.673 -266.687)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1014" data-name="Path 1014" d="M128.708,352.742l-12.918,17.3q2.259,1.585,4.678,2.952l12.059-21.533A29.78,29.78,0,0,1,128.708,352.742Z" transform="translate(-86.178 -261.579)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1015" data-name="Path 1015" d="M148.282,344.183l-13.27,22.306c1.6.9,3.249,1.721,4.944,2.459L151.3,342.54A29.885,29.885,0,0,1,148.282,344.183Z" transform="translate(-100.484 -254.939)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1016" data-name="Path 1016" d="M245.512,299.706l-.014.933c.049.08.1.157.152.235Z" transform="translate(-182.715 -223.059)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1017" data-name="Path 1017" d="M345.666,359.293l-.115,0,10.843,12.365q2.07-1.834,3.951-3.854l-10.1-8.861A30.523,30.523,0,0,1,345.666,359.293Z" transform="translate(-257.18 -267.146)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1018" data-name="Path 1018" d="M109.7,358.291,97.865,371.1q2.091,1.816,4.363,3.421l11.9-16.835A29.9,29.9,0,0,1,109.7,358.291Z" transform="translate(-72.837 -266.212)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1019" data-name="Path 1019" d="M168.063,333.235l-12.732,27.623c1.681.724,3.41,1.36,5.171,1.918l9.7-31.253C169.518,332.124,168.808,332.7,168.063,333.235Z" transform="translate(-115.607 -246.74)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1020" data-name="Path 1020" d="M425.765,317.114l4.1,1.89a55.039,55.039,0,0,0,2.09-5.156l-2.554-.8A30.08,30.08,0,0,1,425.765,317.114Z" transform="translate(-316.88 -232.992)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1021" data-name="Path 1021" d="M366.956,354.693l9.508,8.787q1.868-2.035,3.536-4.252l-8.249-5.829A29.734,29.734,0,0,1,366.956,354.693Z" transform="translate(-273.111 -263.021)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1022" data-name="Path 1022" d="M388.037,345.927l7.811,5.832q1.653-2.216,3.083-4.6l-6.247-3.5A29.681,29.681,0,0,1,388.037,345.927Z" transform="translate(-288.801 -255.772)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1023" data-name="Path 1023" d="M407.931,333.267l5.944,3.539c.945-1.589,1.818-3.224,2.605-4.9l-4.295-1.844A30.07,30.07,0,0,1,407.931,333.267Z" transform="translate(-303.607 -245.648)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1024" data-name="Path 1024" d="M305.393,353.085l11.736,20.958c1.594-.916,3.138-1.913,4.632-2.975l-12.6-16.875A29.566,29.566,0,0,1,305.393,353.085Z" transform="translate(-227.292 -262.787)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1025" data-name="Path 1025" d="M95.607,359.526a30.49,30.49,0,0,1-3.885-.272L81.5,368.218q1.9,2.031,3.989,3.871l11.043-12.595C96.22,359.5,95.919,359.526,95.607,359.526Z" transform="translate(-60.656 -267.379)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1026" data-name="Path 1026" d="M33.372,171.418l-2.436-.827a55.206,55.206,0,0,0-1.529,5.362l1.134.226A29.574,29.574,0,0,1,33.372,171.418Z" transform="translate(-21.886 -126.964)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1027" data-name="Path 1027" d="M24.775,215.1c-.057.472-.123.945-.172,1.426.092-.478.195-.945.306-1.409Z" transform="translate(-18.311 -160.093)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1028" data-name="Path 1028" d="M24.6,277.517c.215,1.386.489,2.751.8,4.1l.739-.166A30.116,30.116,0,0,1,24.6,277.517Z" transform="translate(-18.312 -206.545)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1029" data-name="Path 1029" d="M41.925,151.344l-4.1-1.89c-.773,1.678-1.477,3.4-2.09,5.159l2.554.79A30.421,30.421,0,0,1,41.925,151.344Z" transform="translate(-26.594 -111.233)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1030" data-name="Path 1030" d="M28.736,293.885l-.8.158a56.042,56.042,0,0,0,1.552,5.411l2.173-.736A30.291,30.291,0,0,1,28.736,293.885Z" transform="translate(-20.795 -218.727)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1031" data-name="Path 1031" d="M132.686,71.71,120.95,50.755q-2.4,1.37-4.635,2.969l12.6,16.878A29.847,29.847,0,0,1,132.686,71.71Z" transform="translate(-86.569 -37.775)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1032" data-name="Path 1032" d="M114.544,79.489,102.894,63q-2.242,1.611-4.312,3.43l11.63,12.583A29.553,29.553,0,0,1,114.544,79.489Z" transform="translate(-73.371 -46.891)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1033" data-name="Path 1033" d="M36.643,313.812l-2.288.713a55.1,55.1,0,0,0,2.122,5.194l3.911-1.8A29.954,29.954,0,0,1,36.643,313.812Z" transform="translate(-25.569 -233.558)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1034" data-name="Path 1034" d="M52.909,132.875l-5.944-3.539q-1.417,2.383-2.6,4.9l4.292,1.847A30.193,30.193,0,0,1,52.909,132.875Z" transform="translate(-33.018 -96.26)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1035" data-name="Path 1035" d="M97,89.484c.04,0,.08.006.117.006L86.278,77.121q-2.07,1.834-3.951,3.857l10.1,8.858A30.489,30.489,0,0,1,97,89.484Z" transform="translate(-61.273 -57.398)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1036" data-name="Path 1036" d="M80.805,101.752,71.3,92.962Q69.424,95,67.761,97.214l8.246,5.829A29.74,29.74,0,0,1,80.805,101.752Z" transform="translate(-50.432 -69.188)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1037" data-name="Path 1037" d="M65.975,116.281l-7.813-5.832q-1.653,2.216-3.083,4.6l6.25,3.5A29.411,29.411,0,0,1,65.975,116.281Z" transform="translate(-40.992 -82.203)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1038" data-name="Path 1038" d="M27.425,192.807l-1.068-.24q-.618,2.7-.971,5.506l.172.017A29.634,29.634,0,0,1,27.425,192.807Z" transform="translate(-18.895 -143.32)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1039" data-name="Path 1039" d="M47.175,330.941,43.087,332.7a55.049,55.049,0,0,0,2.637,4.936l5.855-3.482A30.057,30.057,0,0,1,47.175,330.941Z" transform="translate(-32.068 -246.306)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1040" data-name="Path 1040" d="M60.065,344.5l-6.164,3.45q1.456,2.4,3.129,4.627l7.816-5.838A29.831,29.831,0,0,1,60.065,344.5Z" transform="translate(-40.116 -256.397)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1041" data-name="Path 1041" d="M75.013,354.127l-8.237,5.823c1.122,1.48,2.322,2.9,3.585,4.258l9.6-8.87A29.856,29.856,0,0,1,75.013,354.127Z" transform="translate(-49.699 -263.563)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1042" data-name="Path 1042" d="M72.381,13.8a58.58,58.58,0,1,0,58.576,58.581A58.582,58.582,0,0,0,72.381,13.8Z" transform="translate(-10.271 -10.271)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1043" data-name="Path 1043" d="M456.643,196.331a29.873,29.873,0,0,0-1.535-3.931l.739-.169C456.156,193.583,456.431,194.945,456.643,196.331Z" transform="translate(-338.719 -143.07)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1044" data-name="Path 1044" d="M446.933,175.531l-.8.158a29.959,29.959,0,0,0-2.929-4.83l2.17-.739A55.315,55.315,0,0,1,446.933,175.531Z" transform="translate(-329.862 -126.614)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1045" data-name="Path 1045" d="M433.8,154.068l-2.288.71a30.141,30.141,0,0,0-3.748-4.1l3.911-1.8A54.877,54.877,0,0,1,433.8,154.068Z" transform="translate(-318.372 -110.799)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1046" data-name="Path 1046" d="M417.924,133.6l-4.091,1.758a30.026,30.026,0,0,0-4.4-3.212l5.852-3.482Q416.723,131.056,417.924,133.6Z" transform="translate(-304.724 -95.76)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1047" data-name="Path 1047" d="M399.957,114.324l-6.164,3.453a29.832,29.832,0,0,0-4.781-2.239l7.816-5.838Q398.5,111.929,399.957,114.324Z" transform="translate(-289.526 -81.645)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1048" data-name="Path 1048" d="M376.993,92.223c1.26,1.363,2.459,2.783,3.585,4.263l-8.237,5.821a29.6,29.6,0,0,0-4.948-1.208Z" transform="translate(-273.436 -68.638)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1049" data-name="Path 1049" d="M360.47,80.216l-10.224,8.967a29.9,29.9,0,0,0-3.882-.275c-.312,0-.616.023-.925.031l11.046-12.592Q358.572,78.182,360.47,80.216Z" transform="translate(-257.097 -56.823)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1050" data-name="Path 1050" d="M340.528,65.686,328.689,78.5a29.545,29.545,0,0,0-4.42.607l11.9-16.838C337.677,63.336,339.137,64.475,340.528,65.686Z" transform="translate(-241.341 -46.341)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1051" data-name="Path 1051" d="M321.213,53.072l-12.918,17.3a29.464,29.464,0,0,0-3.819,1.28l12.059-21.533Q318.952,51.483,321.213,53.072Z" transform="translate(-226.609 -37.3)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1052" data-name="Path 1052" d="M303.3,42.434l-13.27,22.306a29.791,29.791,0,0,0-3.021,1.643l11.343-26.409C300.049,40.714,301.7,41.538,303.3,42.434Z" transform="translate(-213.611 -29.752)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1053" data-name="Path 1053" d="M287.1,33.966,274.363,61.589c-.745.536-1.452,1.114-2.142,1.715l9.7-31.256Q284.569,32.89,287.1,33.966Z" transform="translate(-202.603 -23.852)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1054" data-name="Path 1054" d="M182.037,23.025,185.41,39.98a9.194,9.194,0,0,1,.544-.916l-3.645-16.1c1.781-.369,3.59-.659,5.428-.856L189.031,36.2c.2-.117.407-.226.613-.327l-1.629-13.8c1.8-.183,3.633-.286,5.486-.3l-.175,13.187c.115,0,.229-.009.344-.009.094,0,.186.006.281.009l-.172-13.193h.052q2.757,0,5.44.261l-1.635,13.811c.209.1.415.206.616.321l1.294-14.1q2.762.283,5.44.825L201.34,39a9.215,9.215,0,0,1,.544.9l3.373-16.95q2.714.563,5.334,1.377l-11.2,33q-.64.653-1.237,1.34l1.437-7.224a9.237,9.237,0,0,1-1.263.873L196.384,60.9c-.16.223-.318.447-.47.676l.782-8.526a8.857,8.857,0,0,1-1.134.315l-1.234,10.419-.135-10.241c-.175.009-.346.014-.524.014-.2,0-.392-.008-.587-.02l-.12,9.316-.011.931-.14-1.165-1.1-9.268a8.925,8.925,0,0,1-1.137-.324l.67,7.3c-.226-.3-.449-.607-.687-.9l-1.618-7.164a9.157,9.157,0,0,1-1.268-.9l1.234,6.2q-.679-.709-1.4-1.374L176.726,24.431A54.79,54.79,0,0,1,182.037,23.025Z" transform="translate(-131.53 -16.203)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1055" data-name="Path 1055" d="M160.738,32.507l9.4,30.266q-1.074-.855-2.224-1.612L155.6,34.454Q158.1,33.359,160.738,32.507Z" transform="translate(-115.806 -24.194)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1056" data-name="Path 1056" d="M140.277,40.546,151.3,66.2a30.143,30.143,0,0,0-3.046-1.517l-12.9-21.671C136.95,42.112,138.6,41.29,140.277,40.546Z" transform="translate(-100.742 -30.177)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1057" data-name="Path 1057" d="M120.951,50.756l11.736,20.955a29.872,29.872,0,0,0-3.768-1.108l-12.6-16.878Q118.554,52.132,120.951,50.756Z" transform="translate(-86.569 -37.776)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1058" data-name="Path 1058" d="M102.894,63l11.65,16.486a29.588,29.588,0,0,0-4.332-.473L98.582,66.434Q100.648,64.617,102.894,63Z" transform="translate(-73.371 -46.891)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1059" data-name="Path 1059" d="M86.277,77.121,97.12,89.49c-.037,0-.077-.006-.117-.006a30.486,30.486,0,0,0-4.572.352l-10.1-8.858Q84.2,78.951,86.277,77.121Z" transform="translate(-61.272 -57.398)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1060" data-name="Path 1060" d="M71.3,92.962l9.508,8.79a29.75,29.75,0,0,0-4.8,1.291l-8.246-5.829Q69.424,95,71.3,92.962Z" transform="translate(-50.433 -69.188)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1061" data-name="Path 1061" d="M58.16,110.449l7.814,5.832a29.424,29.424,0,0,0-4.647,2.268l-6.25-3.5Q56.507,112.671,58.16,110.449Z" transform="translate(-40.992 -82.203)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1062" data-name="Path 1062" d="M46.966,129.336l5.944,3.539a30.2,30.2,0,0,0-4.254,3.213l-4.292-1.847Q45.544,131.715,46.966,129.336Z" transform="translate(-33.018 -96.26)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1063" data-name="Path 1063" d="M37.821,149.454l4.1,1.89a30.405,30.405,0,0,0-3.639,4.06l-2.554-.79C36.344,152.85,37.048,151.132,37.821,149.454Z" transform="translate(-26.593 -111.233)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1064" data-name="Path 1064" d="M30.936,170.591l2.436.827a29.586,29.586,0,0,0-2.832,4.761l-1.134-.226A55.28,55.28,0,0,1,30.936,170.591Z" transform="translate(-21.886 -126.964)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1065" data-name="Path 1065" d="M26.358,192.567l1.068.24a29.658,29.658,0,0,0-1.867,5.283l-.172-.017Q25.743,195.272,26.358,192.567Z" transform="translate(-18.895 -143.32)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1066" data-name="Path 1066" d="M24.775,215.1l.135.017c-.112.464-.215.931-.306,1.409C24.652,216.048,24.718,215.575,24.775,215.1Z" transform="translate(-18.311 -160.093)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1067" data-name="Path 1067" d="M23.047,271.292a57.239,57.239,0,0,0,1.935,5.526l-.739.166C23.935,275.635,23.256,272.678,23.047,271.292Z" transform="translate(-17.153 -201.912)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1068" data-name="Path 1068" d="M27.94,294.042l.8-.157a30.306,30.306,0,0,0,2.929,4.833l-2.173.736A56,56,0,0,1,27.94,294.042Z" transform="translate(-20.795 -218.726)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1069" data-name="Path 1069" d="M34.355,314.525l2.288-.713a29.94,29.94,0,0,0,3.745,4.1l-3.911,1.8A55.1,55.1,0,0,1,34.355,314.525Z" transform="translate(-25.569 -233.558)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1070" data-name="Path 1070" d="M43.087,332.7l4.089-1.758a30.057,30.057,0,0,0,4.4,3.212l-5.855,3.482A54.984,54.984,0,0,1,43.087,332.7Z" transform="translate(-32.068 -246.306)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1071" data-name="Path 1071" d="M53.9,347.949l6.164-3.45a29.814,29.814,0,0,0,4.781,2.239l-7.816,5.838Q55.356,350.347,53.9,347.949Z" transform="translate(-40.117 -256.397)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1072" data-name="Path 1072" d="M70.361,364.207c-1.263-1.36-2.462-2.777-3.585-4.257l8.237-5.824a29.825,29.825,0,0,0,4.945,1.211Z" transform="translate(-49.699 -263.562)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1073" data-name="Path 1073" d="M81.5,368.218l10.224-8.964a30.5,30.5,0,0,0,3.885.272c.312,0,.612-.023.922-.031L85.486,372.089Q83.395,370.256,81.5,368.218Z" transform="translate(-60.656 -267.379)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1074" data-name="Path 1074" d="M97.866,371.1,109.7,358.291a29.9,29.9,0,0,0,4.42-.6l-11.9,16.835Q99.962,372.911,97.866,371.1Z" transform="translate(-72.838 -266.212)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1075" data-name="Path 1075" d="M115.789,370.043l12.918-17.3a29.762,29.762,0,0,0,3.819-1.28L120.467,373Q118.049,371.634,115.789,370.043Z" transform="translate(-86.177 -261.579)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1076" data-name="Path 1076" d="M135.012,366.489l13.27-22.306a29.794,29.794,0,0,0,3.021-1.643l-11.346,26.409C138.262,368.209,136.615,367.385,135.012,366.489Z" transform="translate(-100.484 -254.938)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1077" data-name="Path 1077" d="M155.331,360.858l12.732-27.623c.744-.536,1.454-1.111,2.142-1.712l-9.7,31.253C158.741,362.218,157.012,361.582,155.331,360.858Z" transform="translate(-115.607 -246.74)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1078" data-name="Path 1078" d="M176.58,354.1l11.2-33q.636-.649,1.237-1.343l-7.106,35.717Q179.2,354.915,176.58,354.1Z" transform="translate(-131.422 -237.982)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1079" data-name="Path 1079" d="M215.739,341.39l-1.91-20.783a5.349,5.349,0,0,1-.747.713l2.379,20.1q-2.706.279-5.486.3l.252-19.268c-.149.011-.3.023-.455.023-.109,0-.218-.009-.326-.017l.252,19.269c-.02,0-.037,0-.054,0-1.835,0-3.648-.094-5.437-.266l2.373-20.056a5.451,5.451,0,0,1-.747-.676l-1.9,20.7c-1.841-.183-3.653-.464-5.44-.822l8.6-38.016c.158-.223.315-.447.467-.676l-1.005,10.948a5.28,5.28,0,0,1,1.114-.653l1.48-12.506.158,12.1c.155-.012.312-.023.467-.023a5.1,5.1,0,0,1,.6.037l.143-11.178.014-.933.138,1.168,1.349,11.4a5.245,5.245,0,0,1,1.117.687l-.9-9.795c.226.3.45.6.687.9l8.251,36.467Q218.494,341.088,215.739,341.39Z" transform="translate(-147.728 -223.059)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1080" data-name="Path 1080" d="M268.231,358.6l-6.869-34.54c.452.475.916.933,1.4,1.374l10.779,31.755A54.827,54.827,0,0,1,268.231,358.6Z" transform="translate(-194.521 -241.182)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1081" data-name="Path 1081" d="M282.671,365.192l-9.4-30.266q1.074.855,2.228,1.609l12.311,26.71Q285.3,364.34,282.671,365.192Z" transform="translate(-203.387 -249.272)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1082" data-name="Path 1082" d="M299.041,370.569l-11.023-25.656a30.85,30.85,0,0,0,3.049,1.52L303.96,368.1Q301.572,369.454,299.041,370.569Z" transform="translate(-214.361 -256.705)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1083" data-name="Path 1083" d="M317.129,374.043l-11.736-20.958a29.578,29.578,0,0,0,3.768,1.108l12.6,16.875C320.267,372.13,318.724,373.127,317.129,374.043Z" transform="translate(-227.292 -262.787)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1084" data-name="Path 1084" d="M336.366,374.808l-11.65-16.483a30.456,30.456,0,0,0,4.335.47l11.63,12.583Q338.611,373.194,336.366,374.808Z" transform="translate(-241.673 -266.687)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1085" data-name="Path 1085" d="M356.394,371.656,345.551,359.29c.037,0,.074,0,.115,0a30.523,30.523,0,0,0,4.575-.352l10.1,8.861Q358.468,369.825,356.394,371.656Z" transform="translate(-257.18 -267.146)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1086" data-name="Path 1086" d="M376.464,363.48l-9.508-8.787a29.744,29.744,0,0,0,4.8-1.294L380,359.228Q378.334,361.44,376.464,363.48Z" transform="translate(-273.111 -263.021)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1087" data-name="Path 1087" d="M395.848,351.759l-7.811-5.832a29.659,29.659,0,0,0,4.647-2.268l6.247,3.5Q397.5,349.537,395.848,351.759Z" transform="translate(-288.801 -255.772)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1088" data-name="Path 1088" d="M413.876,336.805l-5.944-3.539a30.088,30.088,0,0,0,4.254-3.21l4.295,1.844C415.694,333.581,414.82,335.216,413.876,336.805Z" transform="translate(-303.608 -245.648)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1089" data-name="Path 1089" d="M429.868,319l-4.1-1.89a30.063,30.063,0,0,0,3.639-4.063l2.554.8A55.012,55.012,0,0,1,429.868,319Z" transform="translate(-316.88 -232.992)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1090" data-name="Path 1090" d="M443.246,298.916l-2.434-.824a29.853,29.853,0,0,0,2.832-4.767l1.134.226C444.351,295.375,443.844,297.167,443.246,298.916Z" transform="translate(-328.079 -218.31)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1091" data-name="Path 1091" d="M453.43,277.14l-1.065-.24a29.721,29.721,0,0,0,1.864-5.283l.172.017Q454.049,274.43,453.43,277.14Z" transform="translate(-336.678 -202.154)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1092" data-name="Path 1092" d="M460.046,266.536l-.135-.014c.115-.467.215-.936.306-1.409C460.172,265.591,460.106,266.064,460.046,266.536Z" transform="translate(-342.294 -197.313)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1093" data-name="Path 1093" d="M235.861,299.706l-1.48,12.506a5.218,5.218,0,0,1,1.638-.409Z" transform="translate(-174.441 -223.059)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1094" data-name="Path 1094" d="M220.846,404.612c1.789.172,3.6.266,5.437.266.017,0,.034,0,.054,0l-.252-19.269a5.312,5.312,0,0,1-2.866-1.051Z" transform="translate(-164.367 -286.21)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1095" data-name="Path 1095" d="M206.554,319.3l1.005-10.948c-.152.229-.309.452-.467.676l-8.6,38.016c1.787.358,3.6.638,5.44.822l1.9-20.7a5.327,5.327,0,0,1,.721-7.865Z" transform="translate(-147.728 -229.492)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1096" data-name="Path 1096" d="M187.783,321.1l-11.2,33q2.615.816,5.334,1.374l7.106-35.717Q188.419,320.453,187.783,321.1Z" transform="translate(-131.422 -237.982)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1097" data-name="Path 1097" d="M261.362,324.056l6.869,34.54a54.827,54.827,0,0,0,5.311-1.411l-10.78-31.754C262.278,324.99,261.814,324.531,261.362,324.056Z" transform="translate(-194.521 -241.182)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1098" data-name="Path 1098" d="M252.2,313.252l.9,9.795a5.339,5.339,0,0,1,.7,7.641l1.91,20.783q2.757-.3,5.426-.856l-8.251-36.467C252.654,313.853,252.43,313.552,252.2,313.252Z" transform="translate(-187.706 -233.141)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1099" data-name="Path 1099" d="M245.234,303.591c-.049-.077-.1-.155-.152-.235l-.143,11.178a5.213,5.213,0,0,1,1.643.461Z" transform="translate(-182.298 -225.776)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1100" data-name="Path 1100" d="M243.4,404.619q2.778-.017,5.486-.3l-2.379-20.1a5.313,5.313,0,0,1-2.855,1.131Z" transform="translate(-181.157 -285.959)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1101" data-name="Path 1101" d="M234.414,355.3l.644.833a1.27,1.27,0,0,0,.154.175.342.342,0,0,0,.129.051v.049h-.85v-.049c.063-.014.095-.043.095-.083a.2.2,0,0,0-.057-.106l-.4-.518-.4.5a.232.232,0,0,0-.063.117c0,.052.034.08.106.089v.049h-.822v-.049a.221.221,0,0,0,.112-.043,1.54,1.54,0,0,0,.16-.183l.653-.816-.6-.776a1.421,1.421,0,0,0-.154-.183.215.215,0,0,0-.123-.037v-.051h.856v.051c-.063.017-.095.049-.095.095a.288.288,0,0,0,.066.126l.335.427.344-.427a.336.336,0,0,0,.077-.135c0-.049-.037-.074-.112-.086v-.051h.836v.051a.175.175,0,0,0-.106.035,1.568,1.568,0,0,0-.166.186Z" transform="translate(-173.374 -263.671)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1102" data-name="Path 1102" d="M243.852,355.3l.647.833a1.6,1.6,0,0,0,.152.175.326.326,0,0,0,.126.051v.049h-.845v-.049c.06-.014.092-.043.092-.083a.243.243,0,0,0-.054-.106l-.4-.518-.4.5a.258.258,0,0,0-.063.117c0,.052.034.08.106.089v.049h-.822v-.049a.232.232,0,0,0,.112-.043,1.541,1.541,0,0,0,.16-.183l.653-.816-.6-.776a1.421,1.421,0,0,0-.154-.183.215.215,0,0,0-.123-.037l0-.051h.859v.051c-.06.017-.092.049-.092.095a.259.259,0,0,0,.066.126l.329.427.347-.427a.316.316,0,0,0,.074-.135c0-.049-.034-.074-.109-.086v-.051h.836v.051a.175.175,0,0,0-.106.035,1.755,1.755,0,0,0-.169.186Z" transform="translate(-180.398 -263.671)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1103" data-name="Path 1103" d="M234.414,373.582l.644.833a1.272,1.272,0,0,0,.154.175.3.3,0,0,0,.129.049v.052h-.85v-.052c.063-.011.095-.04.095-.08a.2.2,0,0,0-.057-.106l-.4-.518-.4.5a.232.232,0,0,0-.063.117c0,.052.034.08.106.086v.052h-.822v-.052a.2.2,0,0,0,.112-.04,1.534,1.534,0,0,0,.16-.183l.653-.816-.6-.776a1.421,1.421,0,0,0-.154-.183.215.215,0,0,0-.123-.037v-.049h.856v.049c-.063.017-.095.049-.095.095a.288.288,0,0,0,.066.126l.335.427.344-.427a.336.336,0,0,0,.077-.134c0-.049-.037-.074-.112-.086v-.049h.836v.049a.175.175,0,0,0-.106.035,1.566,1.566,0,0,0-.166.186Z" transform="translate(-173.374 -277.277)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1104" data-name="Path 1104" d="M243.852,373.582l.647.833a1.6,1.6,0,0,0,.152.175.286.286,0,0,0,.126.049v.052h-.845v-.052c.06-.011.092-.04.092-.08a.243.243,0,0,0-.054-.106l-.4-.518-.4.5a.258.258,0,0,0-.063.117c0,.052.034.08.106.086v.052h-.822v-.052a.206.206,0,0,0,.112-.04,1.535,1.535,0,0,0,.16-.183l.653-.816-.6-.776a1.421,1.421,0,0,0-.154-.183.215.215,0,0,0-.123-.037l0-.049h.859v.049c-.06.017-.092.049-.092.095a.259.259,0,0,0,.066.126l.329.427.347-.427a.317.317,0,0,0,.074-.134c0-.049-.034-.074-.109-.086v-.049h.836v.049a.175.175,0,0,0-.106.035,1.751,1.751,0,0,0-.169.186Z" transform="translate(-180.398 -277.277)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1105" data-name="Path 1105" d="M235.52,367.391h-7.452v-.435h7.452l.349.215-.349.22" transform="translate(-169.742 -273.111)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1106" data-name="Path 1106" d="M221.7,111.106a22.609,22.609,0,0,1-4.131-1.16,21.652,21.652,0,0,0,4.254,1.474" transform="translate(-161.926 -81.828)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1107" data-name="Path 1107" d="M234.987,111.467c1.079,0,3.759-1.119,4.581-1.119,1.008,0,3.862.192,3.862.192a37.817,37.817,0,0,0-4.429-.481c-.684,0-3.055.959-4.149,1.1" transform="translate(-174.791 -81.912)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1108" data-name="Path 1108" d="M241.244,96.706a.482.482,0,0,0,.246-.069h.04a.087.087,0,0,0,.114-.043l.149-.343a.088.088,0,0,0-.046-.115l-.031-.023a.485.485,0,0,0-.951,0l-.029.023a.087.087,0,0,0-.043.115l.149.343a.084.084,0,0,0,.111.043h.035A.535.535,0,0,0,241.244,96.706Z" transform="translate(-179.134 -71.246)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_35" data-name="Line 35" y1="0.601" transform="translate(61.939 25.432)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_36" data-name="Line 36" x1="0.003" y1="0.601" transform="translate(62.291 25.432)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1109" data-name="Path 1109" d="M242.733,113.854a2.22,2.22,0,0,0,1.91-1.38l.149-.415a3.591,3.591,0,0,0,.075-.524" transform="translate(-180.657 -83.011)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_37" data-name="Line 37" y1="0.888" transform="translate(64.215 27.327)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1110" data-name="Path 1110" d="M238.665,104.3v-.146a3.121,3.121,0,0,1-.8-.232.933.933,0,0,1-.5-.59H235.69a.932.932,0,0,1-.5.59,3.115,3.115,0,0,1-.8.232v1.231l0-.029a2.367,2.367,0,0,0,2.13,2.393" transform="translate(-174.45 -76.904)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1111" data-name="Path 1111" d="M242.6,114a2.557,2.557,0,0,0,2.27-1.752l.106-.358a3.506,3.506,0,0,0,.072-.581" transform="translate(-180.557 -82.844)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_38" data-name="Line 38" y1="0.773" transform="translate(64.496 27.356)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1112" data-name="Path 1112" d="M238.128,103.28v-.5a3.638,3.638,0,0,1-.908-.229.591.591,0,0,1-.364-.593h-2.293a.586.586,0,0,1-.364.593,3.626,3.626,0,0,1-.9.229v1.231l0,.275a2.684,2.684,0,0,0,2.454,2.757" transform="translate(-173.632 -75.887)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1113" data-name="Path 1113" d="M252.227,101.757s.9-.93,1.563-1.563" transform="translate(-187.723 -74.57)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1114" data-name="Path 1114" d="M254.583,93.748a.711.711,0,0,1,.143-.115c.135.057.295-.092.3-.24a.272.272,0,0,0-.544-.029c0,.031-2.872,2.806-2.872,2.806" transform="translate(-187.264 -69.295)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1115" data-name="Path 1115" d="M257.6,95.905l1.566,1.569.312-.879.882-.3-1.549-1.583" transform="translate(-191.722 -70.488)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1116" data-name="Path 1116" d="M228.693,101.757s-.9-.93-1.566-1.563" transform="translate(-169.042 -74.57)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1117" data-name="Path 1117" d="M220.95,93.748a.563.563,0,0,0-.143-.115c-.135.057-.292-.092-.3-.24a.271.271,0,1,1,.541-.029c0,.031,2.872,2.806,2.872,2.806" transform="translate(-164.116 -69.295)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1118" data-name="Path 1118" d="M219.845,95.905l-1.569,1.569-.309-.879-.882-.3,1.546-1.583" transform="translate(-161.568 -70.488)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1119" data-name="Path 1119" d="M232.843,124.408h-.009a.23.23,0,0,1-.229-.229.224.224,0,0,1,.129-.2v-.138a4.014,4.014,0,0,1-3.1-2.015l-.006,0a.227.227,0,0,1-.226-.229.225.225,0,0,1,.226-.226.227.227,0,0,1,.229.226.219.219,0,0,1-.051.143l.029.017a3.736,3.736,0,0,0,2.823,1.672l0-1.913" transform="translate(-170.733 -90.326)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1120" data-name="Path 1120" d="M242.834,124.408h.009a.228.228,0,0,0,.1-.432v-.138a4.017,4.017,0,0,0,3.106-2.015l0,0a.227.227,0,0,0,.223-.229.225.225,0,1,0-.45,0,.218.218,0,0,0,.052.143l-.032.017a3.732,3.732,0,0,1-2.82,1.672l-.006-1.913" transform="translate(-180.732 -90.326)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1121" data-name="Path 1121" d="M243.8,169.082a27.951,27.951,0,0,0,26.773,19.933c15.429,0,27.935-15.152,27.935-27.938a27.939,27.939,0,0,0-54.682-8.085" transform="translate(-181.449 -99.092)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1122" data-name="Path 1122" d="M84.663,152.639a27.939,27.939,0,1,0-.052,16.1" transform="translate(-22.286 -98.743)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1123" data-name="Path 1123" d="M360.866,248.908l-1.563-.386-.564,1.675" transform="translate(-266.995 -184.965)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1124" data-name="Path 1124" d="M359.88,242.345l-1.575-.026-.269,1.586-1,.37" transform="translate(-265.729 -180.348)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_39" data-name="Line 39" x2="0.721" y2="1.532" transform="translate(93.842 59.983)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_40" data-name="Line 40" x2="0.349" y2="0.89" transform="translate(95.563 60.298)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1125" data-name="Path 1125" d="M369.276,233.452l.289.6-1.412,1.7.315,1.446" transform="translate(-274.002 -173.749)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_41" data-name="Line 41" y1="0.747" x2="0.93" transform="translate(94.151 54.126)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1126" data-name="Path 1126" d="M361.522,203.762l-1.938-.347-.845-1.5" transform="translate(-266.995 -150.277)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_42" data-name="Line 42" x1="1.475" y2="0.08" transform="translate(91.002 53.513)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_43" data-name="Line 43" x1="1.323" y1="0.633" transform="translate(92.961 55.285)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1127" data-name="Path 1127" d="M362.272,209.517l-2.021-.407.415-1.328" transform="translate(-268.12 -154.644)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_44" data-name="Line 44" y1="1.538" x2="0.721" transform="translate(93.842 55.351)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_45" data-name="Line 45" x1="0.435" y1="0.043" transform="translate(95.803 56.545)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1128" data-name="Path 1128" d="M369.783,217.151l.023-.913-1.652-1.672" transform="translate(-274.003 -159.693)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_46" data-name="Line 46" x1="2.577" transform="translate(93.871 59.732)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_47" data-name="Line 47" x1="2.448" y2="0.003" transform="translate(94 57.51)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1129" data-name="Path 1129" d="M336.068,211.174l.272.083.278-.049.26.115.284-.012.24.149.281.02.223.177.275.06.2.2.266.092.172.226.252.129.14.246.238.155.109.258.215.189.077.272.186.209.043.281.163.232.006.283.129.249-.029.283.1.263-.063.275.063.278-.1.263.029.283-.129.249-.006.284-.163.232-.043.281-.186.209-.077.275-.215.183-.109.261-.238.155-.14.246-.252.129-.172.223-.266.092-.2.2-.275.057-.223.178-.281.023-.24.149-.284-.012-.26.115-.278-.049-.272.083-.272-.083-.278.049-.258-.115-.283.012-.241-.149-.283-.023-.22-.178-.278-.057-.2-.2-.266-.092-.172-.223-.255-.129-.14-.246-.235-.155-.109-.261-.215-.183-.074-.275-.192-.209-.043-.281-.16-.232-.006-.284-.132-.249.031-.283-.1-.263.066-.278-.066-.275.1-.263-.031-.283.132-.249.006-.283.16-.232.043-.281.192-.209.074-.272.215-.189.109-.258.235-.155.14-.246.255-.129.172-.226.266-.092.2-.2.278-.06.22-.177.283-.02.241-.149.283.012.258-.115.278.049.272-.083" transform="translate(-246.864 -157.168)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1130" data-name="Path 1130" d="M334.652,209.282l.3.089.309-.054.289.129.315-.012.266.16.315.029.246.192.306.066.223.223.3.106.189.252.284.14.157.272.261.172.12.289.241.206.083.3.209.232.049.312.18.261.006.312.143.281-.032.312.112.292-.074.306.074.306-.112.295.032.312-.143.281-.006.315-.18.258-.049.309-.209.235-.083.3-.241.206-.12.289-.261.175-.157.272-.284.14-.189.252-.3.1-.223.224-.306.066-.246.195-.315.029-.266.16-.315-.011-.289.129-.309-.054-.3.089-.3-.089-.309.054-.286-.129-.318.011-.266-.16-.312-.029-.246-.195-.309-.066-.22-.224-.3-.1-.189-.252-.281-.14-.157-.272-.261-.175-.123-.289-.238-.206-.083-.3-.215-.235-.046-.309-.177-.258-.006-.315-.149-.281.037-.312-.112-.295.074-.306-.074-.306.112-.292-.037-.312.149-.281.006-.312.177-.261.046-.312.215-.232.083-.3.238-.206.123-.289.261-.172.157-.272.281-.14.189-.252.3-.106.22-.223.309-.066.246-.192.312-.029.266-.16.318.012.286-.129.309.054.3-.089" transform="translate(-245.448 -155.76)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1131" data-name="Path 1131" d="M326.3,214.111l-.3-.14-.478.02-.407-.249-.472-.04-.375-.295-.464-.1-.335-.338-.45-.158-.289-.381-.424-.209-.238-.412-.4-.266-.18-.438-.361-.312-.132-.455-.315-.358-.072-.467-.269-.4-.014-.475-.217-.421.046-.475-.163-.447.109-.464-.109-.461.163-.447-.046-.475.217-.421.014-.478.269-.389.072-.472.315-.355.132-.458.361-.309.18-.438.4-.266.238-.412.424-.209.289-.381.45-.157.335-.338.464-.1.375-.3.472-.04.407-.246.478.02.435-.192.472.077.452-.137.458.137.466-.077.435.192.475-.02.409.246.473.04.375.3.467.1.332.338.449.157.289.381.427.209.238.412.4.266.186.438.361.309.123.458.321.355.069.472.272.389.012.478.22.421-.051.475.169.447-.109.461.109.464-.169.447.051.475-.22.421-.012.475-.272.4-.069.467-.321.358-.123.455-.361.312-.186.438-.4.266-.238.412-.427.209-.289.381-.449.158-.332.338-.467.1-.375.295-.473.04-.409.249-.475-.02-.355.158" transform="translate(-238.158 -148.444)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1132" data-name="Path 1132" d="M355.1,245.442l.258,2.548.2-.135.006-2.648" transform="translate(-264.287 -182.498)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_48" data-name="Line 48" x1="0.999" y2="2.196" transform="translate(90.467 51.463)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_49" data-name="Line 49" y1="2.311" x2="0.836" transform="translate(90.939 51.52)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1133" data-name="Path 1133" d="M340.994,245.442l-.24,2.568-.215-.154,0-2.648" transform="translate(-253.447 -182.498)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_50" data-name="Line 50" x2="0.951" y2="2.196" transform="translate(87.011 51.463)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_51" data-name="Line 51" x1="0.825" y1="2.322" transform="translate(86.708 51.532)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_52" data-name="Line 52" x2="3.121" y2="3.625" transform="translate(82.613 47.907)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_53" data-name="Line 53" x2="4.174" y2="3.238" transform="translate(80.867 48.66)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_54" data-name="Line 54" x2="3.659" y2="2.044" transform="translate(80.601 50.484)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1134" data-name="Path 1134" d="M281,186.615l2.07-1.8,3.293-1.008,3.227-.074,2.371.979,2.5,1.606,3.794.839-.209.338-.85-.066-.435.195-.5.02-.415.266-.573.083-.347.315-.535.1-.3.4-.541.132-.252.421-.487.209-.186.444-.432.261-.117.435-.447.369-.089.467-.349.347-.043.467-.312.415.02.487-.241.435.034.532-.14.452.054.295-.324-.069" transform="translate(-209.136 -136.746)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_55" data-name="Line 55" x2="0.501" y2="0.094" transform="translate(81.024 57.298)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_56" data-name="Line 56" x2="0.429" y2="0.149" transform="translate(81.25 55.909)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1135" data-name="Path 1135" d="M273.531,233.294l.825-.2.092.2" transform="translate(-203.579 -173.483)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1136" data-name="Path 1136" d="M309.827,191.465,311,191.1l.455-2.84,1.712-.693" transform="translate(-230.592 -139.596)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1137" data-name="Path 1137" d="M303.154,188.665l.824-.447.515-1.887,1.486-.6.953-.942" transform="translate(-225.626 -137.529)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1138" data-name="Path 1138" d="M298.072,187.947l.475-.275.658-1.669,1.177-.4.295-.59.4-.1,2.078-.123" transform="translate(-221.843 -137.529)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1139" data-name="Path 1139" d="M280.163,196.317l-1.1-.458-1.134.258-1.34-.132-.644.475-1.045.369.112,1.1.441.438" transform="translate(-204.595 -145.77)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_57" data-name="Line 57" x2="5.718" y2="2.027" transform="translate(77.62 51.317)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1140" data-name="Path 1140" d="M305.74,232.49l.845.421-.461-.825" transform="translate(-227.551 -172.732)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1141" data-name="Path 1141" d="M295.946,215.148l-.057,1.306-.951.988.584.716,1.248.166-1.119.824-1.3-.092-.733-.6" transform="translate(-218.527 -160.126)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1142" data-name="Path 1142" d="M307.106,220.049l.587.295.017.458-.146.387.349.258.713-.824.24-.647-.553-1.205-.475-.241-.478.5,1.048.951-.295.719" transform="translate(-228.567 -162.642)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1143" data-name="Path 1143" d="M304.587,216.388l1.031-.736,1.28.275.773,1.449" transform="translate(-226.692 -160.501)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1144" data-name="Path 1144" d="M299.359,205.677l1.137,1.506,1.557.109.329.438.389-.051" transform="translate(-222.801 -153.077)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1145" data-name="Path 1145" d="M297.209,206.684l.129,1.469.936.641,1.595.329" transform="translate(-221.201 -153.827)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1146" data-name="Path 1146" d="M287.566,202.453l-2.551-2.643-2.293.638-1.686-.218-1.137.916-.733.163-.261.885.845,1.168.424-.106-.4-.953,1.028-.478,1.1.478,1.981-.862.916-.719" transform="translate(-207.578 -148.711)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1147" data-name="Path 1147" d="M283.273,197.686l-1.194-.753-1.7.624-1.119-.166-1.174.532-1.248.5.072,1.483.386.956.4-.312" transform="translate(-206.037 -146.57)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1148" data-name="Path 1148" d="M283.652,208.979l1.8,1.119-.078.919.661.624-.217,1.394,1.079-.295.587-1.357-.968-1.36.2-.9.438-.109" transform="translate(-211.111 -155.535)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1149" data-name="Path 1149" d="M299.435,248.073l-.59.825-.02,1.1-2.053-.094" transform="translate(-220.876 -184.631)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1150" data-name="Path 1150" d="M303.871,245.062l.848.77.492-.075" transform="translate(-226.159 -182.39)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1151" data-name="Path 1151" d="M297.211,239.4l.034,1.028-1.157.7.075.475.733-.183-.57.862-1.12-.092-.438-1.32.587-1.1-.074-.81" transform="translate(-219.385 -177.849)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1152" data-name="Path 1152" d="M302.438,239.263l1.194.879.733-.386" transform="translate(-225.092 -178.074)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1153" data-name="Path 1153" d="M299.426,237.247l.55.6,1.065-.4.624.535" transform="translate(-222.851 -176.574)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1154" data-name="Path 1154" d="M291.232,224.037l-.332,1.031-.933.567,1.174,1.3,1.486-.384" transform="translate(-215.811 -166.742)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1155" data-name="Path 1155" d="M293.985,253.349l.913-.79.057-.678" transform="translate(-218.802 -187.464)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1156" data-name="Path 1156" d="M290.38,240.4l.644.441-.458,1.446.736,1.065" transform="translate(-216.119 -178.924)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1157" data-name="Path 1157" d="M289.1,232.87l-.862.879.435,1.048-.587.808" transform="translate(-214.411 -173.316)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1158" data-name="Path 1158" d="M285.152,238.979l.275-1.065.77-.163" transform="translate(-212.228 -176.949)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1159" data-name="Path 1159" d="M285.183,256.324l-.973,1.214.258.346.7-.424-.389,1.085-.933-.092-.865-.844.79-1.486-.845-.807" transform="translate(-210.569 -190.022)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1160" data-name="Path 1160" d="M289.471,251.734l-.512,1.208,1.245,1.231h1.065l.129-1.082-.6.424-.112-.664" transform="translate(-215.061 -187.356)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1161" data-name="Path 1161" d="M275.625,257.69l-.55,1.082-.827.953.733,1.228,1.085.2.7-1.028-1.082-.112.842-.641" transform="translate(-204.112 -191.789)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1162" data-name="Path 1162" d="M274.472,247.956l1.32,1.56.733-.512.5-.59-.713.149-.461-1.031,1.595-.18.552-.644" transform="translate(-204.279 -183.615)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_58" data-name="Line 58" y1="0.132" x2="1.815" transform="translate(70.946 61.057)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_59" data-name="Line 59" y1="0.753" x2="1.761" transform="translate(71.018 61.498)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_60" data-name="Line 60" x2="1.89" y2="0.386" transform="translate(70.835 60.525)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1163" data-name="Path 1163" d="M277.483,232.478l.55-.24-.258-.587" transform="translate(-206.52 -172.408)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1164" data-name="Path 1164" d="M281.448,219.895l-1.177.584.352.424-.332,1.34,1.045,1.666.112,1.615.272.79" transform="translate(-208.595 -163.659)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1165" data-name="Path 1165" d="M279.566,218.383l.106.55.315.349" transform="translate(-208.07 -162.534)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1166" data-name="Path 1166" d="M279,215.372l-.112.421-.4.292,1.119.111.862.441" transform="translate(-207.262 -160.293)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1167" data-name="Path 1167" d="M285.152,186.942l1.706-.372.441-1.337,1.595-.421,1.538-.888" transform="translate(-212.228 -136.888)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1168" data-name="Path 1168" d="M276.066,194.916l-2.147-.278-.258.5-.676.659.2.9-.292.475.4,1.265.641.512.238-.166" transform="translate(-203.104 -144.862)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1169" data-name="Path 1169" d="M272.093,210.14l-.037.75-.329.828.275.455-1.415,3.272.149.587,1.228.089.387.092-.2.587.238.55-.292.584.4.624.129.22-1.045,1.707.18,1.855-.916,1.337.258.936-.054.733,1.154.624,3.341-1.228,2.826-2.368,2.107-1.744-.037-1.12.458-.77-.238-.916.166-.441-.329-.971,1.171-.513.478-1.142.346-.455-.126-1.228.278-.444-.02-1.208-1.434-.75.1-.876,2.574.741" transform="translate(-201.387 -156.144)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_61" data-name="Line 61" x1="3.126" y2="3.625" transform="translate(92.648 47.884)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_62" data-name="Line 62" x1="4.172" y2="3.241" transform="translate(93.347 48.637)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_63" data-name="Line 63" x1="3.662" y2="2.044" transform="translate(94.126 50.464)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1170" data-name="Path 1170" d="M366.489,186.536l-2.073-1.8-3.293-1.008-3.227-.071-2.368.976-2.5,1.606-3.736.83.235.347.764-.063.435.195.5.02.418.266.57.083.349.315.532.1.3.4.541.132.252.421.487.206.186.447.432.261.12.435.444.369.089.464.349.347.043.47.312.412-.02.487.238.435-.032.532.14.455-.054.292.323-.069" transform="translate(-259.963 -136.688)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_64" data-name="Line 64" x1="0.501" y2="0.092" transform="translate(96.86 57.278)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_65" data-name="Line 65" x1="0.429" y2="0.146" transform="translate(96.708 55.889)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1171" data-name="Path 1171" d="M421.33,233.2l-.825-.2-.092.2" transform="translate(-312.898 -173.416)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1172" data-name="Path 1172" d="M377.969,191.376l-1.177-.367-.455-2.843-1.712-.693" transform="translate(-278.819 -139.529)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1173" data-name="Path 1173" d="M383.375,188.585l-.825-.441-.515-1.89-1.486-.607-.953-.939" transform="translate(-282.518 -137.471)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1174" data-name="Path 1174" d="M384.678,187.868l-.478-.275-.661-1.669-1.171-.406-.3-.584-.4-.1-2.079-.123" transform="translate(-282.518 -137.471)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1175" data-name="Path 1175" d="M402.054,196.238l1.1-.458,1.136.255,1.34-.129.641.478,1.045.366-.109,1.1-.444.438" transform="translate(-299.233 -145.712)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_66" data-name="Line 66" x1="5.715" y2="2.027" transform="translate(95.05 51.294)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1176" data-name="Path 1176" d="M389.34,232.4l-.845.424.458-.828" transform="translate(-289.142 -172.666)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1177" data-name="Path 1177" d="M392.411,215.07l.052,1.3.956.99-.587.716-1.245.163,1.117.828,1.3-.092.736-.607" transform="translate(-291.442 -160.068)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1178" data-name="Path 1178" d="M385.309,219.962l-.59.292-.017.458.146.389-.347.255-.716-.825-.238-.644.55-1.208.478-.24.475.5-1.048.953.295.716" transform="translate(-285.46 -162.576)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1179" data-name="Path 1179" d="M383.967,216.306l-1.031-.733-1.28.278-.773,1.446" transform="translate(-283.477 -160.443)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1180" data-name="Path 1180" d="M388.237,205.6,387.1,207.1l-1.561.109-.329.441-.389-.057" transform="translate(-286.409 -153.019)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1181" data-name="Path 1181" d="M392.578,206.595l-.126,1.471-.939.639-1.595.332" transform="translate(-290.201 -153.76)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1182" data-name="Path 1182" d="M384.757,202.364l2.551-2.643,2.293.644,1.689-.223,1.134.916.736.166.258.882-.845,1.174-.424-.111.4-.951-1.025-.481-1.1.481-1.981-.865-.916-.716" transform="translate(-286.359 -148.645)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1183" data-name="Path 1183" d="M395.527,197.6l1.191-.75,1.7.621,1.122-.163,1.171.532,1.248.5-.072,1.483-.386.956-.4-.315" transform="translate(-294.375 -146.511)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1184" data-name="Path 1184" d="M402.728,208.9l-1.8,1.119.077.916-.664.624.224,1.395-1.082-.3L398.9,211.3l.971-1.357-.2-.9-.441-.112" transform="translate(-296.883 -155.477)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1185" data-name="Path 1185" d="M390.355,248l.59.822.017,1.105,2.053-.094" transform="translate(-290.526 -184.573)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1186" data-name="Path 1186" d="M389.758,244.983l-.845.77-.5-.075" transform="translate(-289.084 -182.332)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1187" data-name="Path 1187" d="M393.112,239.323l-.037,1.028,1.157.7-.074.475-.733-.183.57.864,1.12-.092.441-1.323-.59-1.1.074-.807" transform="translate(-292.55 -177.791)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1188" data-name="Path 1188" d="M389.491,239.173l-1.191.882-.733-.389" transform="translate(-288.451 -178.007)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1189" data-name="Path 1189" d="M391.586,237.169l-.55.6-1.062-.4-.627.532" transform="translate(-289.776 -176.516)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1190" data-name="Path 1190" d="M398.556,223.959l.335,1.028.93.57-1.171,1.3-1.489-.386" transform="translate(-295.592 -166.684)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1191" data-name="Path 1191" d="M400.718,253.267l-.913-.787-.057-.678" transform="translate(-297.516 -187.406)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1192" data-name="Path 1192" d="M404.465,240.315l-.644.441.458,1.449-.736,1.065" transform="translate(-300.341 -178.857)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1193" data-name="Path 1193" d="M405.479,232.792l.865.879-.438,1.045.587.807" transform="translate(-301.782 -173.258)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1194" data-name="Path 1194" d="M409.343,238.893l-.275-1.065-.767-.166" transform="translate(-303.882 -176.882)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1195" data-name="Path 1195" d="M405.771,256.249l.973,1.208-.258.349-.693-.424.384,1.085.933-.092.865-.844-.79-1.486.845-.807" transform="translate(-301.999 -189.964)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1196" data-name="Path 1196" d="M400.966,251.645l.513,1.211-1.245,1.231h-1.065L399.042,253l.6.421.112-.659" transform="translate(-296.992 -187.289)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1197" data-name="Path 1197" d="M414.587,257.6l.55,1.082.828.953-.733,1.234-1.082.2-.7-1.028,1.082-.112-.842-.639" transform="translate(-307.715 -191.722)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1198" data-name="Path 1198" d="M412.808,247.878l-1.32,1.557-.733-.513-.493-.587.71.146.461-1.025-1.595-.186-.552-.641" transform="translate(-304.615 -183.557)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_67" data-name="Line 67" x1="1.818" y1="0.129" transform="translate(105.624 61.037)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_68" data-name="Line 68" x1="1.761" y1="0.75" transform="translate(105.607 61.478)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_69" data-name="Line 69" x1="1.887" y2="0.386" transform="translate(105.664 60.505)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1199" data-name="Path 1199" d="M418.445,232.388l-.55-.238.258-.59" transform="translate(-311.023 -172.342)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1200" data-name="Path 1200" d="M411.867,219.816l1.174.581-.352.424.332,1.34-1.042,1.669-.112,1.615-.275.787" transform="translate(-306.332 -163.601)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1201" data-name="Path 1201" d="M416.74,218.294l-.109.55-.315.352" transform="translate(-309.848 -162.468)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1202" data-name="Path 1202" d="M412.767,215.282l.112.421.4.295-1.119.111-.862.438" transform="translate(-306.115 -160.226)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1203" data-name="Path 1203" d="M397.019,186.86l-1.706-.369-.441-1.337-1.592-.421-1.537-.888" transform="translate(-291.559 -136.829)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1204" data-name="Path 1204" d="M412.241,194.834l2.144-.275.258.493.676.664-.2.9.295.478-.406,1.265-.641.512-.238-.166" transform="translate(-306.815 -144.803)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1205" data-name="Path 1205" d="M386.79,210.051l.037.75.332.828-.278.458,1.414,3.27-.149.587-1.228.089-.387.092.2.587-.238.55.292.587-.4.624-.132.218L387.3,220.4l-.18,1.852.916,1.34-.258.933.054.736-1.154.621-3.341-1.225-2.823-2.368-2.107-1.744.035-1.12-.458-.767.241-.922-.169-.438.332-.973-1.174-.513-.478-1.137-.346-.461.126-1.228-.275-.438.017-1.214,1.434-.75-.1-.876-2.574.747" transform="translate(-279.111 -156.077)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_70" data-name="Line 70" y2="0.544" transform="translate(85.969 71.15)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1206" data-name="Path 1206" d="M333.161,254.8l-.521,1.019.02.089-.206-.152-.14-.186-.149-.261.074-.321.009-.02-.109-.427.1-.424.009-.02-.109-.424.1-2.15" transform="translate(-247.198 -187.198)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1207" data-name="Path 1207" d="M337.636,257.858l.635,2.812.415-.2-.166-2.119" transform="translate(-251.289 -191.914)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_71" data-name="Line 71" x2="0.733" y2="3.175" transform="translate(85.826 66.889)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1208" data-name="Path 1208" d="M332.6,260.24l.441.309L334,259.9" transform="translate(-247.54 -193.43)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1209" data-name="Path 1209" d="M332.217,269l.533,1.008.833-.661" transform="translate(-247.256 -200.204)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_72" data-name="Line 72" y2="2.019" transform="translate(84.967 68.109)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1210" data-name="Path 1210" d="M332.52,254.925l2.345,1.34-.077-1.022" transform="translate(-247.481 -189.731)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1211" data-name="Path 1211" d="M333.911,274.147c.272,0,.249.286.249.6,0,.329.023.424-.209.424h-2.093c-.243,0-.192-.095-.192-.424,0-.3-.023-.593.18-.593Z" transform="translate(-246.843 -204.037)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1212" data-name="Path 1212" d="M334.66,278.133v.578h-2.073v-.575" transform="translate(-247.532 -207.003)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_73" data-name="Line 73" y2="0.544" transform="translate(92.431 71.15)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_74" data-name="Line 74" x2="0.538" y2="1.557" transform="translate(92.216 67.428)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1213" data-name="Path 1213" d="M356.4,259.893l.289,1.423-.653,1.492-.083-.115-.112-.427.1-.421.009-.023v-.089l-.112-.424.1-1.031.009-.023-.032-.132.014-.031.235-.143.229-.057.386-.232.246.444.183-.26.306-.16.129-.5.269-.154.1-.037h.266" transform="translate(-264.837 -192.755)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_75" data-name="Line 75" x2="0.003" y2="2.751" transform="translate(92.193 67.35)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_76" data-name="Line 76" x1="0.667" y1="0.839" transform="translate(92.683 66.929)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_77" data-name="Line 77" x1="0.206" y1="1.864" transform="translate(90.899 65.486)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1214" data-name="Path 1214" d="M362.813,257.5l-.346-1,.45-.51.069-.223.22-.223-.08-.321-.006-.02.109-.427-.1-.424-.006-.02.109-.424-.106-2.067" transform="translate(-269.77 -187.439)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_78" data-name="Line 78" y2="2.019" transform="translate(93.436 68.109)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_79" data-name="Line 79" x1="2.399" y2="1.28" transform="translate(90.962 65.194)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1215" data-name="Path 1215" d="M356.393,274.147c-.272,0-.252.286-.252.6,0,.329-.02.424.215.424h2.09c.243,0,.192-.095.192-.424,0-.3.023-.593-.18-.593Z" transform="translate(-265.061 -204.037)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1216" data-name="Path 1216" d="M356.892,278.133v.578h2.073v-.575" transform="translate(-265.621 -207.003)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1217" data-name="Path 1217" d="M344.72,265.628a1.065,1.065,0,0,0-.109.467,1.041,1.041,0,1,0,2.081,0,1.082,1.082,0,0,0-.08-.407" transform="translate(-256.48 -197.697)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1218" data-name="Path 1218" d="M344.705,262.336a1.068,1.068,0,0,0-.094.441,1.041,1.041,0,1,0,2-.407" transform="translate(-256.48 -195.246)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1219" data-name="Path 1219" d="M344.72,258.843a1.055,1.055,0,0,0-.109.467,1.041,1.041,0,1,0,2.081,0,1.061,1.061,0,0,0-.08-.407" transform="translate(-256.48 -192.647)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1220" data-name="Path 1220" d="M344.611,254.232a1.041,1.041,0,1,0,1.039-1.042A1.042,1.042,0,0,0,344.611,254.232Z" transform="translate(-256.48 -188.439)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1221" data-name="Path 1221" d="M345.909,174.128V173.12h.994v1.008" transform="translate(-257.447 -128.847)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1222" data-name="Path 1222" d="M352.9,174.128V173.12h.994v1.008" transform="translate(-262.646 -128.847)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1223" data-name="Path 1223" d="M339,174.128V173.12H340v1.008" transform="translate(-252.306 -128.847)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1224" data-name="Path 1224" d="M360.745,174.14l.352-.407.066-.613H359.8v1.008" transform="translate(-267.787 -128.847)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1225" data-name="Path 1225" d="M331.151,174.128l-.3-.4-.066-.613h1.36v1.008" transform="translate(-246.19 -128.847)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1226" data-name="Path 1226" d="M383.94,295.636l1.286-.14,2.605-2.339.73-1.454" transform="translate(-285.751 -217.102)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1227" data-name="Path 1227" d="M385.518,298.621l1.306-.223,3.141-2.651.461-1.649" transform="translate(-286.926 -218.885)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1228" data-name="Path 1228" d="M294.892,291.3l.727,1.457,2.608,2.339,1.285.137" transform="translate(-219.477 -216.802)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1229" data-name="Path 1229" d="M292.194,293.694l.458,1.649,3.144,2.651,1.306.224" transform="translate(-217.469 -218.585)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1230" data-name="Path 1230" d="M292.939,283.106l-.163.61-2.946-3.081-2.156-3.51-.421.928,1.183,2.792-.618.6-1.271-2.894-.352,1.411.8,3.493,4.011,3.4,1.114.3.919-2.471,1.243-.713" transform="translate(-213.003 -206.253)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1231" data-name="Path 1231" d="M312.525,288.544l3.069,1.976,4.134,1.214h2.293" transform="translate(-232.6 -214.752)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1232" data-name="Path 1232" d="M358.163,289.149l-3.055,1.821-3.839,1.214h-2.013" transform="translate(-259.938 -215.202)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1233" data-name="Path 1233" d="M355.109,296.269l-3.839,1.214h-2.013" transform="translate(-259.938 -220.501)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1234" data-name="Path 1234" d="M310.733,294.344l3.527,2.233,3.988,1.243h2.293" transform="translate(-231.267 -219.068)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1235" data-name="Path 1235" d="M358.2,294.344l-3.516,2.233-3.985,1.243h-2.013" transform="translate(-259.513 -219.068)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1236" data-name="Path 1236" d="M354.684,303.076l-3.985,1.243h-2.013" transform="translate(-259.513 -225.568)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1237" data-name="Path 1237" d="M308.058,297.859l4.211,2.611,3.988,1.068h2.293" transform="translate(-229.275 -221.685)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1238" data-name="Path 1238" d="M318.012,287.17H316.3l-3.321-.888L310,284.565l-.086-.086-.438-.693L308.7,285.9l-.352,1.131-.636.386" transform="translate(-229.017 -211.211)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1239" data-name="Path 1239" d="M358.764,297.691l-4.08,2.654-3.985,1.068h-2.013" transform="translate(-259.513 -221.56)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1240" data-name="Path 1240" d="M347.846,287.17h1.927l3.324-.888,2.975-1.718.083-.086.441-.693.776,2.116.355,1.131.367.295" transform="translate(-258.888 -211.211)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1241" data-name="Path 1241" d="M386.626,279.957l-.8,3.493-4.011,3.4-1.117.3-.882-2.359-1.386-.807,1.371-.882.593.424,2.6-2.895,2.156-3.51.421.928-1.108,2.8.693.687,1.119-2.995.352,1.411" transform="translate(-281.652 -206.254)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1242" data-name="Path 1242" d="M354.684,308.069l-3.985,1.068h-2.013" transform="translate(-259.513 -229.284)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1243" data-name="Path 1243" d="M348.686,287.17H350.4l3.324-.888,2.975-1.718.083-.086.441-.693L358,285.9l.355,1.131.412.309" transform="translate(-259.513 -211.211)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1244" data-name="Path 1244" d="M352.454,177.106s.043,2.531-.5,2.943a21.641,21.641,0,0,1-3.293,1.409,7.6,7.6,0,0,1,.481-2.826,5.7,5.7,0,0,1,1.641-1.526" transform="translate(-259.496 -131.813)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_80" data-name="Line 80" x1="4.051" transform="translate(88.906 45.293)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1245" data-name="Path 1245" d="M332.225,177.106s-.046,2.531.5,2.943a22.263,22.263,0,0,0,3.547,1.411,9.832,9.832,0,0,0-.742-2.829,5.632,5.632,0,0,0-1.638-1.526" transform="translate(-247.261 -131.813)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_81" data-name="Line 81" x2="4.046" transform="translate(84.964 45.293)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_82" data-name="Line 82" x1="1.738" y1="0.086" transform="translate(85.339 64.599)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_83" data-name="Line 83" y1="0.16" x2="0.879" transform="translate(86.269 63.224)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_84" data-name="Line 84" x2="2.073" y2="0.896" transform="translate(83.73 60.874)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1246" data-name="Path 1246" d="M327.963,242.8l2.021-.146.664,2.571" transform="translate(-244.09 -180.598)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_85" data-name="Line 85" x1="1.755" y2="1.921" transform="translate(82.839 59.972)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_86" data-name="Line 86" x2="0.372" y2="2.345" transform="translate(84.692 62.096)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_87" data-name="Line 87" x2="0.435" y2="0.04" transform="translate(82.195 60.276)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1247" data-name="Path 1247" d="M323.1,235.848l1.1.558.183,2.491" transform="translate(-240.474 -175.532)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_88" data-name="Line 88" x1="0.71" y2="2.542" transform="translate(84.088 54.927)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_89" data-name="Line 89" x1="0.575" y1="0.985" transform="translate(83.355 54.117)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_90" data-name="Line 90" x1="0.985" y2="0.014" transform="translate(85.339 52.239)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1248" data-name="Path 1248" d="M330.448,203.109l1.337.278.847-1.495" transform="translate(-245.94 -150.261)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_91" data-name="Line 91" x2="1.609" transform="translate(85.823 53.587)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1249" data-name="Path 1249" d="M329.553,209.474l1.211.324.4-2.062" transform="translate(-245.273 -154.61)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1250" data-name="Path 1250" d="M321.425,211.584l2.079-1.652-.289-1.58" transform="translate(-239.224 -155.068)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_92" data-name="Line 92" x1="0.578" y1="1.643" transform="translate(83.094 55.795)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_93" data-name="Line 93" x2="2.591" y2="0.046" transform="translate(81.986 59.723)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_94" data-name="Line 94" x2="2.448" y2="0.003" transform="translate(81.986 57.501)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1251" data-name="Path 1251" d="M307.776,260.193l-.553-.1-.352-.664.3-.69.724-.195.6.447.029.75-.3.258.009.011,1.323,1.855,3.556,2.866,3.891.916-.014-.012V261.7" transform="translate(-228.392 -192.422)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1252" data-name="Path 1252" d="M345.68,293.224l-.129-.673.487-.57.75.023.449.6-.154.593" transform="translate(-257.18 -217.31)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1253" data-name="Path 1253" d="M350.174,261.579v3.937l-.014.011,3.891-.916,3.553-2.866,1.263-1.784.063-.057-.375-.324.029-.75.6-.447.727.192.3.69-.349.661-.378.072.035.029-1.091,2.288-3.917,3.158-4.486,1.094-.009-.008-.178.438" transform="translate(-260.371 -192.305)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1254" data-name="Path 1254" d="M319.944,271.779l-.177-.438-.009.009-4.486-1.094-3.919-3.158-1.088-2.288" transform="translate(-230.917 -197.088)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <path id="Path_1255" data-name="Path 1255" d="M343.558,225.429l.043-2.657.9.029s.753.072.753.707a.653.653,0,0,1-.779.664l-.736-.043,2.236,1.4" transform="translate(-255.697 -165.8)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
                <line id="Line_95" data-name="Line 95" x1="0.043" y2="1.815" transform="translate(89.11 58.314)" fill="none" stroke="#f7f5f3" stroke-miterlimit="10" stroke-width="0.3"/>
            </g>
        </g>
    </g>
</svg>
