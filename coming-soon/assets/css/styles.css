/*!
* Start Bootstrap - Coming Soon v6.0.7 (https://startbootstrap.com/theme/coming-soon)
* Copyright 2013-2023 Start Bootstrap
* Licensed under MIT (https://github.com/StartBootstrap/startbootstrap-coming-soon/blob/master/LICENSE)
*/

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Tinos:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap');

/* Global Styles */
html, body {
  height: 100%;
  font-family: 'DM Sans', sans-serif;
}

body {
  background: #2a5555;
  overflow: hidden;
}

/* Background Video Styles */
video.bg-video {
  position: fixed;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  transform: translateX(-50%) translateY(-50%);
  z-index: 0;
}

/* Mobile fallback for devices that don't support autoplay */
@media (pointer: coarse) and (hover: none) {
  body {
    background: url("../img/bg-mobile-fallback.jpg") #2a5555 no-repeat center center scroll;
    background-size: cover;
  }
  body video {
    display: none;
  }
}

/* Masthead Styles */
.masthead {
  position: relative;
  overflow: hidden;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.masthead:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.85);
}

.masthead-content {
  position: relative;
  z-index: 3;
  text-align: center;
}

.masthead-content img {
  max-width: 100%;
  height: auto;
  margin-bottom: 2rem;
}

.masthead-content p {
  font-size: 1.5rem;
  font-weight: 300;
  color: #fff;
  margin: 0;
}

/* Social Icons */
.social-icons {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 4;
  padding: 1rem;
}

.social-icons .btn {
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.social-icons .btn:hover {
  transform: scale(1.1);
  background-color: #fff !important;
  color: #2a5555 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .masthead-content p {
    font-size: 1.2rem;
  }
  
  .social-icons {
    position: fixed;
    bottom: 1rem;
    left: 50%;
    right: auto;
    transform: translateX(-50%);
  }
  
  .social-icons .d-flex {
    flex-direction: row !important;
  }
}

@media (max-width: 576px) {
  .masthead-content img {
    width: 200px;
  }
  
  .masthead-content p {
    font-size: 1rem;
  }
}

/* Animation for logo */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.masthead-content img,
.masthead-content p {
  animation: fadeInUp 1s ease-out;
}

.masthead-content p {
  animation-delay: 0.3s;
}

/* Loading animation for social icons */
.social-icons .btn {
  animation: fadeInUp 1s ease-out;
  animation-delay: 0.6s;
  animation-fill-mode: both;
}
