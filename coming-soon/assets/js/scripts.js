/*!
* Start Bootstrap - Coming Soon v6.0.7 (https://startbootstrap.com/theme/coming-soon)
* Copyright 2013-2023 Start Bootstrap
* Licensed under MIT (https://github.com/StartBootstrap/startbootstrap-coming-soon/blob/master/LICENSE)
*/

// Coming Soon Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize any animations or interactions here
    
    // Add smooth hover effects to social icons
    const socialButtons = document.querySelectorAll('.social-icons .btn');
    
    socialButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
    
    // Add click tracking for social media links (optional)
    socialButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // You can add analytics tracking here if needed
            console.log('Social media link clicked:', this.href);
        });
    });
    
    // Ensure video plays on mobile devices that support it
    const video = document.querySelector('.bg-video');
    if (video) {
        video.play().catch(function(error) {
            console.log('Video autoplay failed:', error);
            // Fallback to background image is already handled in CSS
        });
    }
    
    // Add a subtle parallax effect to the masthead content (optional)
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const masthead = document.querySelector('.masthead-content');
        if (masthead) {
            masthead.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });
});

// Add any additional functionality here as needed
