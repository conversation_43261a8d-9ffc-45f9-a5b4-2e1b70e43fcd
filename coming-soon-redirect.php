<?php
/**
 * Plugin Name: Coming Soon Page Redirect for Guests
 * Description: Redirects all guest users to a coming soon page with its assets.
 * Version: 1.0.0
 */

if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly.
}

class Coming_Soon_Plugin {
	public function __construct() {
		// Hook into the template_redirect action
		add_action('template_redirect', [$this, 'serve_coming_soon_template']);
	}

	public function serve_coming_soon_template() {
		if (!is_user_logged_in()) {
			// Get the current request URI and remove leading/trailing slashes
			$path = trim($_SERVER['REQUEST_URI'], '/');

			// Remove any query strings
			$path = strtok($path, '?');

			// Sanitize the path
			$path = preg_replace('/[^a-zA-Z0-9-_]/', '', $path);

			// For coming soon, we always serve the same page regardless of path
			$template_file = plugin_dir_path(__FILE__) . 'coming-soon/coming-soon.php';

			// Double check the file exists and is within our plugin directory
			$real_template = realpath($template_file);
			$plugin_dir = realpath(plugin_dir_path(__FILE__) . 'coming-soon');

			if ($real_template && strpos($real_template, $plugin_dir) === 0 && file_exists($real_template)) {
				include($real_template);
				exit; // Prevent further WordPress processing
			}
		}
	}
}

new Coming_Soon_Plugin();
